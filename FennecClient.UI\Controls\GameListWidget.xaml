<UserControl x:Class="FennecClient.UI.Controls.GameListWidget"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="200">
    
    <UserControl.Resources>
        <!-- Converters -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Game Item Style -->
        <Style x:Key="GameItemStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#e4e5e6"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="Margin" Value="2,1"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" Background="{TemplateBinding Background}" 
                                CornerRadius="4" BorderThickness="0">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="24"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- Game Icon -->
                                <Border Grid.Column="0" Width="16" Height="16" 
                                        CornerRadius="2" Background="#444449"
                                        VerticalAlignment="Center">
                                    <TextBlock Text="🎮" FontSize="10" 
                                               HorizontalAlignment="Center" 
                                               VerticalAlignment="Center"/>
                                </Border>
                                
                                <!-- Game Name -->
                                <TextBlock Grid.Column="1" 
                                           Text="{Binding Name}" 
                                           FontSize="11"
                                           FontWeight="Medium"
                                           Foreground="{TemplateBinding Foreground}"
                                           VerticalAlignment="Center" 
                                           Margin="6,0,4,0"
                                           TextTrimming="CharacterEllipsis"/>
                                
                                <!-- Running Indicator -->
                                <Ellipse Grid.Column="2" 
                                         Width="6" Height="6" 
                                         Fill="#4CAF50" 
                                         VerticalAlignment="Center"
                                         Margin="0,0,4,0"
                                         Visibility="{Binding IsRunning, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#333338"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#fa0000"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Loading Style -->
        <Style x:Key="LoadingTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#888888"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Margin" Value="8"/>
        </Style>
    </UserControl.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#333338" CornerRadius="6" Margin="4,4,4,2">
            <Grid Height="28">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           Text="Games" 
                           Foreground="#e4e5e6" 
                           FontSize="12" 
                           FontWeight="SemiBold"
                           VerticalAlignment="Center" 
                           Margin="8,0,0,0"/>
                
                <Button Grid.Column="1" 
                        x:Name="RefreshButton"
                        Click="RefreshButton_Click"
                        Width="24" Height="20" 
                        Background="Transparent" 
                        BorderThickness="0" 
                        Cursor="Hand"
                        Margin="0,0,4,0">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border x:Name="border" Background="{TemplateBinding Background}" 
                                    CornerRadius="3">
                                <TextBlock Text="🔄" FontSize="10" 
                                           HorizontalAlignment="Center" 
                                           VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="border" Property="Background" Value="#444449"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="border" Property="Background" Value="#fa0000"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </Grid>
        </Border>
        
        <!-- Search Box -->
        <Border Grid.Row="1" Background="#2a2a2f" CornerRadius="4" Margin="4,2,4,4">
            <TextBox x:Name="SearchTextBox" 
                     Background="Transparent" 
                     Foreground="#e4e5e6" 
                     BorderThickness="0" 
                     FontSize="11"
                     Padding="6,4"
                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                     TextChanged="SearchTextBox_TextChanged">
                <TextBox.Style>
                    <Style TargetType="TextBox">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="TextBox">
                                    <Grid>
                                        <ScrollViewer x:Name="PART_ContentHost" 
                                                      VerticalAlignment="Center"/>
                                        <TextBlock Text="Search games..." 
                                                   Foreground="#666666" 
                                                   FontSize="11"
                                                   Margin="6,4"
                                                   VerticalAlignment="Center"
                                                   IsHitTestVisible="False">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource TemplatedParent}}" Value="">
                                                            <Setter Property="Visibility" Value="Visible"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </TextBox.Style>
            </TextBox>
        </Border>
        
        <!-- Games List -->
        <ScrollViewer Grid.Row="2" 
                      VerticalScrollBarVisibility="Auto" 
                      HorizontalScrollBarVisibility="Disabled"
                      Margin="2,0">
            <StackPanel x:Name="GamesPanel">
                <!-- Loading indicator -->
                <TextBlock x:Name="LoadingText" 
                           Text="Scanning for games..." 
                           Style="{StaticResource LoadingTextStyle}"
                           Visibility="Visible"/>
                
                <!-- No games found -->
                <TextBlock x:Name="NoGamesText" 
                           Text="No games found" 
                           Style="{StaticResource LoadingTextStyle}"
                           Visibility="Collapsed"/>
                
                <!-- Games will be added here dynamically -->
            </StackPanel>
        </ScrollViewer>
        
        <!-- Footer with game count -->
        <Border Grid.Row="3" Background="#2a2a2f" CornerRadius="4" Margin="4,4,4,2">
            <TextBlock x:Name="GameCountText" 
                       Text="0 games found" 
                       Foreground="#888888" 
                       FontSize="10" 
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Margin="4,3"/>
        </Border>
    </Grid>
</UserControl>
