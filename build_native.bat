@echo off
echo Building FennecClient.Native C++ DLL...

REM Try to find MSBuild
set MSBUILD_PATH=""

REM Check common Visual Studio 2022 paths
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
) else (
    REM Try to use MSBuild from PATH
    where msbuild >nul 2>nul
    if %ERRORLEVEL% EQU 0 (
        set MSBUILD_PATH=msbuild
    ) else (
        echo ERROR: MSBuild not found!
        echo Please install Visual Studio 2022 or run this from a Developer Command Prompt.
        echo.
        echo Alternative: Open FennecClient.sln in Visual Studio and build the solution.
        pause
        exit /b 1
    )
)

echo Found MSBuild: %MSBUILD_PATH%
echo.

REM Create output directories if they don't exist
if not exist "FennecClient.App\bin\Debug\net8.0-windows" mkdir "FennecClient.App\bin\Debug\net8.0-windows"
if not exist "FennecClient.App\bin\Release\net8.0-windows" mkdir "FennecClient.App\bin\Release\net8.0-windows"

REM Build Debug configuration
echo Building Debug configuration...
%MSBUILD_PATH% FennecClient.Native\FennecClient.Native.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VCTargetsPath="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo DEBUG BUILD FAILED!
    echo Trying alternative build method...
    
    REM Try building the entire solution
    %MSBUILD_PATH% FennecClient.sln /p:Configuration=Debug /p:Platform="Any CPU"
    
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo SOLUTION BUILD ALSO FAILED!
        echo Please check the error messages above.
        pause
        exit /b 1
    )
)

echo.
echo Building Release configuration...
%MSBUILD_PATH% FennecClient.Native\FennecClient.Native.vcxproj /p:Configuration=Release /p:Platform=x64

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo RELEASE BUILD FAILED!
    echo Trying alternative build method...
    
    REM Try building the entire solution
    %MSBUILD_PATH% FennecClient.sln /p:Configuration=Release /p:Platform="Any CPU"
    
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo SOLUTION BUILD ALSO FAILED!
        echo Please check the error messages above.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo DLL files should be located at:
echo - FennecClient.App\bin\Debug\net8.0-windows\FennecClient.Native.dll
echo - FennecClient.App\bin\Release\net8.0-windows\FennecClient.Native.dll
echo.

REM Check if DLL files were actually created
if exist "FennecClient.App\bin\Debug\net8.0-windows\FennecClient.Native.dll" (
    echo ✓ Debug DLL found
) else (
    echo ✗ Debug DLL NOT found
)

if exist "FennecClient.App\bin\Release\net8.0-windows\FennecClient.Native.dll" (
    echo ✓ Release DLL found
) else (
    echo ✗ Release DLL NOT found
)

echo.
echo You can now run FennecClient.App and the C++ recoil engine should work!
echo.
pause
