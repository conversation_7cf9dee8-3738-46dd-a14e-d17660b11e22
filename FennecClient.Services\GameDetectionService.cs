using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Win32;
using System.Diagnostics;

namespace FennecClient.Services
{
    public class GameInfo
    {
        public string Name { get; set; } = string.Empty;
        public string ExecutablePath { get; set; } = string.Empty;
        public string InstallPath { get; set; } = string.Empty;
        public string IconPath { get; set; } = string.Empty;
        public string Platform { get; set; } = string.Empty; // Steam, Epic, Origin, etc.
        public bool IsRunning { get; set; }
        public DateTime LastPlayed { get; set; }
    }

    public class GameDetectionService
    {
        private readonly List<GameInfo> _detectedGames = new();
        private List<string> _commonGameDirectories = new();

        private readonly List<string> _gameExecutableExtensions = new()
        {
            ".exe"
        };

        private readonly List<string> _commonGameExecutableNames = new()
        {
            "game.exe", "launcher.exe", "start.exe", "play.exe", "run.exe"
        };

        public event EventHandler<List<GameInfo>>? GamesDetected;

        private void BuildGameDirectoriesList()
        {
            _commonGameDirectories.Clear();

            try
            {
                // Get all available drives
                var drives = DriveInfo.GetDrives()
                    .Where(d => d.IsReady && d.DriveType == DriveType.Fixed)
                    .Select(d => d.Name.TrimEnd('\\'))
                    .ToList();

                Console.WriteLine($"Found drives: {string.Join(", ", drives)}");

                foreach (var drive in drives)
                {
                    // Steam directories
                    _commonGameDirectories.Add($@"{drive}\Program Files (x86)\Steam\steamapps\common");
                    _commonGameDirectories.Add($@"{drive}\Program Files\Steam\steamapps\common");

                    // Epic Games
                    _commonGameDirectories.Add($@"{drive}\Program Files (x86)\Epic Games");
                    _commonGameDirectories.Add($@"{drive}\Program Files\Epic Games");

                    // Origin
                    _commonGameDirectories.Add($@"{drive}\Program Files (x86)\Origin Games");
                    _commonGameDirectories.Add($@"{drive}\Program Files\Origin Games");
                    _commonGameDirectories.Add($@"{drive}\Program Files (x86)\EA Games");
                    _commonGameDirectories.Add($@"{drive}\Program Files\EA Games");

                    // Ubisoft
                    _commonGameDirectories.Add($@"{drive}\Program Files (x86)\Ubisoft\Ubisoft Game Launcher\games");
                    _commonGameDirectories.Add($@"{drive}\Program Files\Ubisoft\Ubisoft Game Launcher\games");
                    _commonGameDirectories.Add($@"{drive}\Program Files (x86)\Ubisoft Game Launcher\games");
                    _commonGameDirectories.Add($@"{drive}\Program Files\Ubisoft Game Launcher\games");

                    // Battle.net
                    _commonGameDirectories.Add($@"{drive}\Program Files (x86)\Battle.net");
                    _commonGameDirectories.Add($@"{drive}\Program Files\Battle.net");

                    // GOG Galaxy
                    _commonGameDirectories.Add($@"{drive}\Program Files (x86)\GOG Galaxy\Games");
                    _commonGameDirectories.Add($@"{drive}\Program Files\GOG Galaxy\Games");
                    _commonGameDirectories.Add($@"{drive}\GOG Games");

                    // Microsoft Store / Xbox
                    _commonGameDirectories.Add($@"{drive}\Program Files\WindowsApps");
                    _commonGameDirectories.Add($@"{drive}\XboxGames");

                    // Common game folders
                    _commonGameDirectories.Add($@"{drive}\Games");
                    _commonGameDirectories.Add($@"{drive}\Game");
                    _commonGameDirectories.Add($@"{drive}\Gaming");

                    // SteamLibrary (additional Steam libraries)
                    _commonGameDirectories.Add($@"{drive}\SteamLibrary\steamapps\common");

                    // Other common locations
                    _commonGameDirectories.Add($@"{drive}\Program Files (x86)\Games");
                    _commonGameDirectories.Add($@"{drive}\Program Files\Games");
                }

                Console.WriteLine($"Built {_commonGameDirectories.Count} potential game directories across {drives.Count} drives");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error building game directories list: {ex.Message}");

                // Fallback to C: drive only
                _commonGameDirectories.AddRange(new[]
                {
                    @"C:\Program Files (x86)\Steam\steamapps\common",
                    @"C:\Program Files\Steam\steamapps\common",
                    @"C:\Program Files (x86)\Epic Games",
                    @"C:\Program Files\Epic Games",
                    @"C:\Games"
                });
            }
        }

        public async Task<List<GameInfo>> DetectGamesAsync()
        {
            _detectedGames.Clear();

            try
            {
                Console.WriteLine("Starting game detection from games.ini...");

                // Build directories list for all drives
                BuildGameDirectoriesList();

                // Load games from games.ini file
                await LoadGamesFromIniAsync();

                // Remove duplicates and sort
                var uniqueGames = _detectedGames
                    .GroupBy(g => g.Name.ToLowerInvariant())
                    .Select(g => g.First())
                    .OrderBy(g => g.Name)
                    .ToList();

                Console.WriteLine($"Detected {uniqueGames.Count} games total from games.ini");

                GamesDetected?.Invoke(this, uniqueGames);
                return uniqueGames;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error detecting games: {ex.Message}");
                return new List<GameInfo>();
            }
        }

        private async Task LoadGamesFromIniAsync()
        {
            try
            {
                var iniPath = @"c:\Users\<USER>\Desktop\Fennec\games.ini";

                if (!File.Exists(iniPath))
                {
                    Console.WriteLine($"games.ini file not found at: {iniPath}");
                    return;
                }

                Console.WriteLine($"Loading games from: {iniPath}");

                await Task.Run(() =>
                {
                    var lines = File.ReadAllLines(iniPath);

                    foreach (var line in lines)
                    {
                        if (string.IsNullOrWhiteSpace(line) || !line.Contains(":"))
                            continue;

                        try
                        {
                            // Parse line format: "Game Name": ["exe1.exe", "exe2.exe"],
                            var colonIndex = line.IndexOf(':');
                            if (colonIndex == -1) continue;

                            // Extract game name (remove quotes)
                            var gameName = line.Substring(0, colonIndex).Trim().Trim('"');

                            // Extract executables part
                            var executablesPart = line.Substring(colonIndex + 1).Trim();
                            if (!executablesPart.StartsWith("[") || !executablesPart.Contains("]"))
                                continue;

                            // Parse executables array
                            var startBracket = executablesPart.IndexOf('[');
                            var endBracket = executablesPart.IndexOf(']');
                            var executablesString = executablesPart.Substring(startBracket + 1, endBracket - startBracket - 1);

                            var executables = executablesString
                                .Split(',')
                                .Select(e => e.Trim().Trim('"'))
                                .Where(e => !string.IsNullOrEmpty(e))
                                .ToList();

                            // Find the game executable in all directories
                            foreach (var executable in executables)
                            {
                                var foundPath = FindExecutableInDirectories(executable);
                                if (!string.IsNullOrEmpty(foundPath))
                                {
                                    var installPath = Path.GetDirectoryName(foundPath) ?? "";
                                    var platform = GetPlatformFromPath(installPath);

                                    _detectedGames.Add(new GameInfo
                                    {
                                        Name = gameName,
                                        ExecutablePath = foundPath,
                                        InstallPath = installPath,
                                        Platform = platform,
                                        IconPath = foundPath,
                                        IsRunning = false
                                    });

                                    Console.WriteLine($"Found game: {gameName} at {foundPath}");
                                    break; // Found one executable, no need to check others
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error parsing line '{line}': {ex.Message}");
                        }
                    }
                });

                Console.WriteLine($"Loaded {_detectedGames.Count} games from games.ini");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading games from ini: {ex.Message}");
            }
        }

        private string FindExecutableInDirectories(string executableName)
        {
            try
            {
                // Search in all common game directories
                foreach (var directory in _commonGameDirectories)
                {
                    if (!Directory.Exists(directory))
                        continue;

                    try
                    {
                        // Search recursively in subdirectories (up to 3 levels deep)
                        var foundFiles = Directory.GetFiles(directory, executableName, SearchOption.AllDirectories);
                        if (foundFiles.Length > 0)
                        {
                            return foundFiles[0];
                        }
                    }
                    catch (Exception ex)
                    {
                        // Skip directories we can't access
                        Console.WriteLine($"Cannot access directory {directory}: {ex.Message}");
                    }
                }

                // Also search in common Windows directories
                var systemPaths = new[]
                {
                    Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles),
                    Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86),
                    @"C:\Windows\System32",
                    @"C:\Windows\SysWOW64"
                };

                foreach (var systemPath in systemPaths)
                {
                    try
                    {
                        var foundFiles = Directory.GetFiles(systemPath, executableName, SearchOption.AllDirectories);
                        if (foundFiles.Length > 0)
                        {
                            return foundFiles[0];
                        }
                    }
                    catch
                    {
                        // Skip if we can't access
                    }
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error searching for executable {executableName}: {ex.Message}");
                return string.Empty;
            }
        }

        private async Task DetectSteamGamesAsync()
        {
            try
            {
                // Try to find Steam installation
                var steamPath = GetSteamInstallPath();
                if (string.IsNullOrEmpty(steamPath))
                    return;

                var steamAppsPath = Path.Combine(steamPath, "steamapps", "common");
                if (!Directory.Exists(steamAppsPath))
                    return;

                Console.WriteLine($"Scanning Steam games in: {steamAppsPath}");
                
                await Task.Run(() =>
                {
                    var gameDirectories = Directory.GetDirectories(steamAppsPath);
                    foreach (var gameDir in gameDirectories)
                    {
                        var gameName = Path.GetFileName(gameDir);
                        var gameExe = FindGameExecutable(gameDir);
                        
                        if (!string.IsNullOrEmpty(gameExe))
                        {
                            _detectedGames.Add(new GameInfo
                            {
                                Name = gameName,
                                ExecutablePath = gameExe,
                                InstallPath = gameDir,
                                Platform = "Steam",
                                IconPath = gameExe
                            });
                        }
                    }
                });
                
                Console.WriteLine($"Found {_detectedGames.Count(g => g.Platform == "Steam")} Steam games");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error detecting Steam games: {ex.Message}");
            }
        }

        private async Task DetectRegistryGamesAsync()
        {
            try
            {
                Console.WriteLine("Scanning registry for installed games...");
                
                await Task.Run(() =>
                {
                    // Check both 32-bit and 64-bit registry locations
                    var registryPaths = new[]
                    {
                        @"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
                        @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
                    };

                    foreach (var registryPath in registryPaths)
                    {
                        try
                        {
                            using var key = Registry.LocalMachine.OpenSubKey(registryPath);
                            if (key == null) continue;

                            foreach (var subKeyName in key.GetSubKeyNames())
                            {
                                using var subKey = key.OpenSubKey(subKeyName);
                                if (subKey == null) continue;

                                var displayName = subKey.GetValue("DisplayName")?.ToString();
                                var installLocation = subKey.GetValue("InstallLocation")?.ToString();
                                var displayIcon = subKey.GetValue("DisplayIcon")?.ToString();

                                if (!string.IsNullOrEmpty(displayName) && 
                                    !string.IsNullOrEmpty(installLocation) && 
                                    Directory.Exists(installLocation) &&
                                    IsLikelyGame(displayName, installLocation))
                                {
                                    var gameExe = FindGameExecutable(installLocation);
                                    if (!string.IsNullOrEmpty(gameExe))
                                    {
                                        _detectedGames.Add(new GameInfo
                                        {
                                            Name = displayName,
                                            ExecutablePath = gameExe,
                                            InstallPath = installLocation,
                                            Platform = "Registry",
                                            IconPath = displayIcon ?? gameExe
                                        });
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error reading registry path {registryPath}: {ex.Message}");
                        }
                    }
                });
                
                Console.WriteLine($"Found {_detectedGames.Count(g => g.Platform == "Registry")} games from registry");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error detecting registry games: {ex.Message}");
            }
        }

        private async Task DetectDirectoryGamesAsync()
        {
            try
            {
                Console.WriteLine("Scanning common game directories...");
                
                await Task.Run(() =>
                {
                    foreach (var directory in _commonGameDirectories)
                    {
                        if (!Directory.Exists(directory))
                            continue;

                        try
                        {
                            var gameDirectories = Directory.GetDirectories(directory);
                            foreach (var gameDir in gameDirectories)
                            {
                                var gameName = Path.GetFileName(gameDir);
                                var gameExe = FindGameExecutable(gameDir);
                                
                                if (!string.IsNullOrEmpty(gameExe))
                                {
                                    var platform = GetPlatformFromPath(directory);
                                    _detectedGames.Add(new GameInfo
                                    {
                                        Name = gameName,
                                        ExecutablePath = gameExe,
                                        InstallPath = gameDir,
                                        Platform = platform,
                                        IconPath = gameExe
                                    });
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error scanning directory {directory}: {ex.Message}");
                        }
                    }
                });
                
                Console.WriteLine($"Found {_detectedGames.Count(g => g.Platform != "Steam" && g.Platform != "Registry")} games from directories");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error detecting directory games: {ex.Message}");
            }
        }

        private string GetSteamInstallPath()
        {
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\WOW6432Node\Valve\Steam") ??
                               Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Valve\Steam");
                return key?.GetValue("InstallPath")?.ToString() ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        private string FindGameExecutable(string gameDirectory)
        {
            try
            {
                if (!Directory.Exists(gameDirectory))
                    return string.Empty;

                // Look for common game executable names first
                foreach (var commonName in _commonGameExecutableNames)
                {
                    var commonPath = Path.Combine(gameDirectory, commonName);
                    if (File.Exists(commonPath))
                        return commonPath;
                }

                // Look for any .exe files
                var exeFiles = Directory.GetFiles(gameDirectory, "*.exe", SearchOption.TopDirectoryOnly)
                    .Where(f => !IsSystemFile(f))
                    .ToList();

                if (exeFiles.Count == 1)
                    return exeFiles[0];

                // If multiple exe files, try to find the main one
                var mainExe = exeFiles.FirstOrDefault(f => 
                    Path.GetFileNameWithoutExtension(f).Equals(Path.GetFileName(gameDirectory), StringComparison.OrdinalIgnoreCase));
                
                return mainExe ?? exeFiles.FirstOrDefault() ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        private bool IsSystemFile(string filePath)
        {
            var fileName = Path.GetFileName(filePath).ToLowerInvariant();
            var systemFiles = new[] { "uninstall.exe", "setup.exe", "install.exe", "update.exe", "patch.exe", "redist.exe" };
            return systemFiles.Any(sf => fileName.Contains(sf));
        }

        private bool IsLikelyGame(string displayName, string installLocation)
        {
            var gameKeywords = new[] { "game", "play", "steam", "epic", "origin", "ubisoft", "blizzard", "battle.net" };
            var excludeKeywords = new[] { "microsoft", "visual", "runtime", "redistributable", "driver", "update" };
            
            var nameLower = displayName.ToLowerInvariant();
            var pathLower = installLocation.ToLowerInvariant();
            
            // Exclude obvious non-games
            if (excludeKeywords.Any(keyword => nameLower.Contains(keyword)))
                return false;
            
            // Include if contains game keywords or is in game directories
            return gameKeywords.Any(keyword => nameLower.Contains(keyword) || pathLower.Contains(keyword)) ||
                   _commonGameDirectories.Any(dir => pathLower.StartsWith(dir.ToLowerInvariant()));
        }

        private string GetPlatformFromPath(string path)
        {
            var pathLower = path.ToLowerInvariant();
            if (pathLower.Contains("steam")) return "Steam";
            if (pathLower.Contains("epic")) return "Epic Games";
            if (pathLower.Contains("origin")) return "Origin";
            if (pathLower.Contains("ubisoft")) return "Ubisoft";
            if (pathLower.Contains("battle.net")) return "Battle.net";
            if (pathLower.Contains("gog")) return "GOG";
            return "Unknown";
        }

        public bool IsGameRunning(GameInfo game)
        {
            try
            {
                var processName = Path.GetFileNameWithoutExtension(game.ExecutablePath);
                return Process.GetProcessesByName(processName).Length > 0;
            }
            catch
            {
                return false;
            }
        }

        public void LaunchGame(GameInfo game)
        {
            try
            {
                if (File.Exists(game.ExecutablePath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = game.ExecutablePath,
                        WorkingDirectory = Path.GetDirectoryName(game.ExecutablePath),
                        UseShellExecute = true
                    });
                    Console.WriteLine($"Launched game: {game.Name}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error launching game {game.Name}: {ex.Message}");
            }
        }
    }
}
