using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using FennecClient.Services;

namespace FennecClient.UI.Controls
{
    public partial class GameListWidget : UserControl
    {
        private readonly GameDetectionService _gameDetectionService;
        private List<GameInfo> _allGames = new();
        private List<GameInfo> _filteredGames = new();
        private string _searchText = string.Empty;

        public GameListWidget()
        {
            InitializeComponent();
            _gameDetectionService = new GameDetectionService();
            _gameDetectionService.GamesDetected += OnGamesDetected;
            
            Loaded += GameListWidget_Loaded;
        }

        private async void GameListWidget_Loaded(object sender, RoutedEventArgs e)
        {
            await RefreshGamesAsync();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshGamesAsync();
        }

        private async Task RefreshGamesAsync()
        {
            try
            {
                // Show loading state
                LoadingText.Visibility = Visibility.Visible;
                NoGamesText.Visibility = Visibility.Collapsed;
                ClearGamesList();
                GameCountText.Text = "Scanning...";

                // Detect games
                _allGames = await _gameDetectionService.DetectGamesAsync();
                
                // Apply current search filter
                FilterGames();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error refreshing games: {ex.Message}");
                LoadingText.Visibility = Visibility.Collapsed;
                NoGamesText.Visibility = Visibility.Visible;
                NoGamesText.Text = "Error loading games";
                GameCountText.Text = "Error";
            }
        }

        private void OnGamesDetected(object? sender, List<GameInfo> games)
        {
            Dispatcher.Invoke(() =>
            {
                _allGames = games;
                FilterGames();
            });
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _searchText = SearchTextBox.Text?.ToLowerInvariant() ?? string.Empty;
            FilterGames();
        }

        private void FilterGames()
        {
            try
            {
                // Filter games based on search text
                _filteredGames = string.IsNullOrEmpty(_searchText) 
                    ? _allGames.ToList()
                    : _allGames.Where(g => g.Name.ToLowerInvariant().Contains(_searchText)).ToList();

                // Update UI
                UpdateGamesList();
                UpdateGameCount();
                UpdateVisibility();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error filtering games: {ex.Message}");
            }
        }

        private void UpdateGamesList()
        {
            try
            {
                ClearGamesList();

                foreach (var game in _filteredGames)
                {
                    var gameButton = CreateGameButton(game);
                    GamesPanel.Children.Add(gameButton);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating games list: {ex.Message}");
            }
        }

        private Button CreateGameButton(GameInfo game)
        {
            var button = new Button
            {
                Style = (Style)FindResource("GameItemStyle"),
                DataContext = game,
                ToolTip = $"{game.Name}\nPlatform: {game.Platform}\nPath: {game.InstallPath}"
            };

            button.Click += (sender, e) => OnGameClicked(game);
            
            return button;
        }

        private void OnGameClicked(GameInfo game)
        {
            try
            {
                Console.WriteLine($"Game clicked: {game.Name}");
                
                // Check if game is already running
                if (_gameDetectionService.IsGameRunning(game))
                {
                    var result = MessageBox.Show(
                        $"{game.Name} is already running.\n\nDo you want to launch another instance?",
                        "Game Already Running",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);
                    
                    if (result != MessageBoxResult.Yes)
                        return;
                }

                // Launch the game
                _gameDetectionService.LaunchGame(game);
                
                // Update the game's last played time
                game.LastPlayed = DateTime.Now;
                
                // Refresh the running status after a short delay
                Task.Delay(2000).ContinueWith(_ => 
                {
                    Dispatcher.Invoke(() =>
                    {
                        game.IsRunning = _gameDetectionService.IsGameRunning(game);
                        UpdateGamesList(); // Refresh to show running indicator
                    });
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error launching game {game.Name}: {ex.Message}");
                MessageBox.Show(
                    $"Failed to launch {game.Name}:\n{ex.Message}",
                    "Launch Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void ClearGamesList()
        {
            // Remove all game buttons but keep the loading/no games text blocks
            var itemsToRemove = GamesPanel.Children
                .OfType<Button>()
                .ToList();

            foreach (var item in itemsToRemove)
            {
                GamesPanel.Children.Remove(item);
            }
        }

        private void UpdateGameCount()
        {
            var count = _filteredGames.Count;
            var total = _allGames.Count;
            
            if (string.IsNullOrEmpty(_searchText))
            {
                GameCountText.Text = $"{count} game{(count != 1 ? "s" : "")} found";
            }
            else
            {
                GameCountText.Text = $"{count} of {total} game{(total != 1 ? "s" : "")}";
            }
        }

        private void UpdateVisibility()
        {
            LoadingText.Visibility = Visibility.Collapsed;
            
            if (_filteredGames.Count == 0)
            {
                NoGamesText.Visibility = Visibility.Visible;
                NoGamesText.Text = string.IsNullOrEmpty(_searchText) 
                    ? "No games found" 
                    : "No games match your search";
            }
            else
            {
                NoGamesText.Visibility = Visibility.Collapsed;
            }
        }

        // Public method to refresh games from outside
        public async Task RefreshAsync()
        {
            await RefreshGamesAsync();
        }

        // Public method to get detected games
        public List<GameInfo> GetDetectedGames()
        {
            return _allGames.ToList();
        }

        // Public method to check if a specific game is running
        public bool IsGameRunning(string gameName)
        {
            var game = _allGames.FirstOrDefault(g => 
                g.Name.Equals(gameName, StringComparison.OrdinalIgnoreCase));
            
            return game != null && _gameDetectionService.IsGameRunning(game);
        }
    }
}
