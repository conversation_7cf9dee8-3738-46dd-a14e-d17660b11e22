﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "869096E6A613914DA9107BAA9E4CB38726F25E5F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FennecClient.UI.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FennecClient.App {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 206 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image LogoImage;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LogoFallback;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MNKButton;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ControllerButton;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CrosshairButton;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConfigsButton;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OverviewButton;
        
        #line default
        #line hidden
        
        
        #line 372 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton StreamproofToggle;
        
        #line default
        #line hidden
        
        
        #line 448 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ContentArea;
        
        #line default
        #line hidden
        
        
        #line 451 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MNKContent;
        
        #line default
        #line hidden
        
        
        #line 461 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ControllerContent;
        
        #line default
        #line hidden
        
        
        #line 471 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CrosshairContent;
        
        #line default
        #line hidden
        
        
        #line 482 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ConfigsContent;
        
        #line default
        #line hidden
        
        
        #line 492 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid OverviewContent;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FennecClient.App;V1.0.0.0;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 188 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.LogoImage = ((System.Windows.Controls.Image)(target));
            return;
            case 3:
            this.LogoFallback = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.MNKButton = ((System.Windows.Controls.Button)(target));
            
            #line 225 "..\..\..\MainWindow.xaml"
            this.MNKButton.Click += new System.Windows.RoutedEventHandler(this.NavigateToTab);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ControllerButton = ((System.Windows.Controls.Button)(target));
            
            #line 249 "..\..\..\MainWindow.xaml"
            this.ControllerButton.Click += new System.Windows.RoutedEventHandler(this.NavigateToTab);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CrosshairButton = ((System.Windows.Controls.Button)(target));
            
            #line 273 "..\..\..\MainWindow.xaml"
            this.CrosshairButton.Click += new System.Windows.RoutedEventHandler(this.NavigateToTab);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ConfigsButton = ((System.Windows.Controls.Button)(target));
            
            #line 298 "..\..\..\MainWindow.xaml"
            this.ConfigsButton.Click += new System.Windows.RoutedEventHandler(this.NavigateToTab);
            
            #line default
            #line hidden
            return;
            case 8:
            this.OverviewButton = ((System.Windows.Controls.Button)(target));
            
            #line 322 "..\..\..\MainWindow.xaml"
            this.OverviewButton.Click += new System.Windows.RoutedEventHandler(this.NavigateToTab);
            
            #line default
            #line hidden
            return;
            case 9:
            this.StreamproofToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 373 "..\..\..\MainWindow.xaml"
            this.StreamproofToggle.Click += new System.Windows.RoutedEventHandler(this.StreamproofToggle_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ContentArea = ((System.Windows.Controls.Grid)(target));
            return;
            case 11:
            this.MNKContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 12:
            this.ControllerContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 13:
            this.CrosshairContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 14:
            this.ConfigsContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 15:
            this.OverviewContent = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

