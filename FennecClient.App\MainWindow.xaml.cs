using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using Microsoft.Win32;
using WinForms = System.Windows.Forms;

namespace FennecClient.App
{
    public partial class MainWindow : Window
    {
        private const int WH_KEYBOARD_LL = 13;
        private const int WM_KEYDOWN = 0x0100;
        private const int VK_INSERT = 0x2D;
        
        // Streamproof constants
        private const uint WDA_NONE = 0x00000000;
        private const uint WDA_MONITOR = 0x00000001;
        private const uint WDA_EXCLUDEFROMCAPTURE = 0x00000011;
        
        // Extended window styles
        private const int GWL_EXSTYLE = -20;
        private const uint WS_EX_NOREDIRECTIONBITMAP = 0x00200000;
        
        private delegate IntPtr LowLevelKeyboardProc(int nCode, IntPtr wParam, IntPtr lParam);
        private LowLevelKeyboardProc _proc = HookCallback;
        private static IntPtr _hookID = IntPtr.Zero;
        private static MainWindow _instance;
        private bool _isSidebarVisible = true;
        private Dictionary<string, List<string>> _supportedGames;
        private string _gamesConfigPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Fennec", "user_games.ini");

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr SetWindowsHookEx(int idHook, LowLevelKeyboardProc lpfn, IntPtr hMod, uint dwThreadId);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool UnhookWindowsHookEx(IntPtr hhk);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr CallNextHookEx(IntPtr hhk, int nCode, IntPtr wParam, IntPtr lParam);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("user32.dll")]
        private static extern uint SetWindowDisplayAffinity(IntPtr hwnd, uint dwAffinity);

        [DllImport("user32.dll")]
        private static extern uint GetWindowLong(IntPtr hWnd, int nIndex);

        [DllImport("user32.dll")]
        private static extern uint SetWindowLong(IntPtr hWnd, int nIndex, uint dwNewLong);

        [DllImport("user32.dll")]
        private static extern bool InvalidateRect(IntPtr hWnd, IntPtr lpRect, bool bErase);

        [DllImport("user32.dll")]
        private static extern bool UpdateWindow(IntPtr hWnd);

        public MainWindow()
        {
            try
            {
                InitializeComponent();
                Console.WriteLine("MainWindow initialized successfully");
                
                _instance = this;
                SetupHotkey();
                SetupWindowConstraints();
                SetupLogo();

                // Apply streamproof protection by default and update toggle icons
                this.Loaded += (s, e) => {
                    ApplyStreamproofProtection(true);
                    UpdateToggleIcons();
                    LoadSupportedGames();
                    LoadUserGames();
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing MainWindow: {ex.Message}\n{ex.StackTrace}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyStreamproofProtection(bool enable)
        {
            try
            {
                var hwnd = new WindowInteropHelper(this).Handle;
                if (hwnd != IntPtr.Zero)
                {
                    if (enable)
                    {
                        // Make window completely invisible to screen capture
                        SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE);
                        System.Threading.Thread.Sleep(50); // Small delay
                        
                        // Add extended style to prevent DWM redirection
                        uint exStyle = GetWindowLong(hwnd, GWL_EXSTYLE);
                        SetWindowLong(hwnd, GWL_EXSTYLE, exStyle | WS_EX_NOREDIRECTIONBITMAP);
                        System.Threading.Thread.Sleep(50); // Small delay
                        
                        // Force window update
                        InvalidateRect(hwnd, IntPtr.Zero, true);
                        UpdateWindow(hwnd);
                    }
                    else
                    {
                        // Remove protection
                        SetWindowDisplayAffinity(hwnd, WDA_NONE);
                        System.Threading.Thread.Sleep(50); // Small delay
                        
                        // Remove extended style
                        uint exStyle = GetWindowLong(hwnd, GWL_EXSTYLE);
                        SetWindowLong(hwnd, GWL_EXSTYLE, exStyle & ~WS_EX_NOREDIRECTIONBITMAP);
                        System.Threading.Thread.Sleep(50); // Small delay
                        
                        // Force window update
                        InvalidateRect(hwnd, IntPtr.Zero, true);
                        UpdateWindow(hwnd);
                    }
                    
                    // Also protect overlay windows if they exist
                    foreach (Window window in System.Windows.Application.Current.Windows)
                    {
                        if (window != this && window.GetType().Name.Contains("Overlay"))
                        {
                            var overlayHwnd = new WindowInteropHelper(window).Handle;
                            if (overlayHwnd != IntPtr.Zero)
                            {
                                if (enable)
                                {
                                    SetWindowDisplayAffinity(overlayHwnd, WDA_EXCLUDEFROMCAPTURE);
                                    System.Threading.Thread.Sleep(25);
                                    uint overlayExStyle = GetWindowLong(overlayHwnd, GWL_EXSTYLE);
                                    SetWindowLong(overlayHwnd, GWL_EXSTYLE, overlayExStyle | WS_EX_NOREDIRECTIONBITMAP);
                                    System.Threading.Thread.Sleep(25);
                                    InvalidateRect(overlayHwnd, IntPtr.Zero, true);
                                    UpdateWindow(overlayHwnd);
                                }
                                else
                                {
                                    SetWindowDisplayAffinity(overlayHwnd, WDA_NONE);
                                    System.Threading.Thread.Sleep(25);
                                    uint overlayExStyle = GetWindowLong(overlayHwnd, GWL_EXSTYLE);
                                    SetWindowLong(overlayHwnd, GWL_EXSTYLE, overlayExStyle & ~WS_EX_NOREDIRECTIONBITMAP);
                                    System.Threading.Thread.Sleep(25);
                                    InvalidateRect(overlayHwnd, IntPtr.Zero, true);
                                    UpdateWindow(overlayHwnd);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error applying streamproof protection: {ex.Message}");
            }
        }



        private void SetupLogo()
        {
            // Handle title bar logo loading failure
            TitleBarLogoImage.ImageFailed += (s, e) => {
                TitleBarLogoImage.Visibility = Visibility.Collapsed;
                TitleBarLogoFallback.Visibility = Visibility.Visible;
            };
        }

        private void SetupHotkey()
        {
            _hookID = SetHook(_proc);
        }

        private void SetupWindowConstraints()
        {
            // Get primary screen bounds
            var primaryScreen = WinForms.Screen.PrimaryScreen;
            
            // Set window constraints to stay within screen bounds with margins
            this.MaxWidth = primaryScreen.Bounds.Width - 40; // 20px margin on each side
            this.MaxHeight = primaryScreen.Bounds.Height - 40; // 20px margin on top/bottom
            
            // Constrain window movement
            this.LocationChanged += MainWindow_LocationChanged;
        }

        private void MainWindow_LocationChanged(object sender, EventArgs e)
        {
            var primaryScreen = WinForms.Screen.PrimaryScreen;
            var margin = 20;
            
            // Ensure window stays within screen bounds
            if (this.Left < margin)
                this.Left = margin;
            
            if (this.Top < margin)
                this.Top = margin;
                
            if (this.Left + this.Width > primaryScreen.Bounds.Width - margin)
                this.Left = primaryScreen.Bounds.Width - this.Width - margin;
                
            if (this.Top + this.Height > primaryScreen.Bounds.Height - margin)
                this.Top = primaryScreen.Bounds.Height - this.Height - margin;
        }

        private static IntPtr SetHook(LowLevelKeyboardProc proc)
        {
            using (Process curProcess = Process.GetCurrentProcess())
            using (ProcessModule curModule = curProcess.MainModule)
            {
                return SetWindowsHookEx(WH_KEYBOARD_LL, proc,
                    GetModuleHandle(curModule.ModuleName), 0);
            }
        }

        private static IntPtr HookCallback(int nCode, IntPtr wParam, IntPtr lParam)
        {
            if (nCode >= 0 && wParam == (IntPtr)WM_KEYDOWN)
            {
                int vkCode = Marshal.ReadInt32(lParam);
                if (vkCode == VK_INSERT)
                {
                    // Toggle window visibility
                    _instance?.Dispatcher.Invoke(() => {
                        if (_instance.Visibility == Visibility.Visible)
                        {
                            _instance.Hide();
                            Console.WriteLine("MainWindow hidden with INSERT key");
                        }
                        else
                        {
                            _instance.Show();
                            _instance.Activate();
                            Console.WriteLine("MainWindow shown with INSERT key");
                        }
                    });
                }
            }
            return CallNextHookEx(_hookID, nCode, wParam, lParam);
        }

        protected override void OnClosed(EventArgs e)
        {
            UnhookWindowsHookEx(_hookID);
            base.OnClosed(e);
        }



        // Window Controls
        private void TitleBar_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            DragMove();
        }

        private void MinimizeWindow_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void MaximizeWindow_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
        }

        private void CloseWindow_Click(object sender, RoutedEventArgs e)
        {
            Console.WriteLine("Application closing...");
            Console.WriteLine("Press any key to close console...");
            this.Close();
        }

        private void ToggleSidebar_Click(object sender, RoutedEventArgs e)
        {
            _isSidebarVisible = !_isSidebarVisible;

            if (_isSidebarVisible)
            {
                // Expand sidebar to normal size
                SidebarColumn.Width = new GridLength(220);
                SidebarColumn.MinWidth = 180;
                SidebarColumn.MaxWidth = 400;
                SplitterColumn.Width = new GridLength(5);
            }
            else
            {
                // Collapse sidebar to minimal size (just show a thin strip)
                SidebarColumn.Width = new GridLength(40);
                SidebarColumn.MinWidth = 40;
                SidebarColumn.MaxWidth = 40;
                SplitterColumn.Width = new GridLength(0);
            }

            // Update toggle icons in both hamburger menu and sidebar button
            UpdateToggleIcons();

            Console.WriteLine($"Sidebar toggled: {(_isSidebarVisible ? "Expanded" : "Collapsed")}");
        }

        private void UpdateToggleIcons()
        {
            // Find the toggle icon in the sidebar button and update it
            if (SidebarToggleButton?.Template != null)
            {
                var toggleIcon = SidebarToggleButton.Template.FindName("ToggleIcon", SidebarToggleButton) as TextBlock;
                if (toggleIcon != null)
                {
                    // Use different icons based on state
                    // &#xE76B; = ChevronLeft (collapse), &#xE76C; = ChevronRight (expand)
                    toggleIcon.Text = _isSidebarVisible ? "\uE76B" : "\uE76C";
                }
            }
        }

        private void LoadSupportedGames()
        {
            _supportedGames = new Dictionary<string, List<string>>();
            string gamesIniPath = @"c:\Users\<USER>\Desktop\Fennec\games.ini";

            try
            {
                if (File.Exists(gamesIniPath))
                {
                    var lines = File.ReadAllLines(gamesIniPath);
                    foreach (var line in lines)
                    {
                        if (string.IsNullOrWhiteSpace(line)) continue;

                        // Parse format: "Game Name": ["exe1.exe", "exe2.exe"]
                        var parts = line.Split(':');
                        if (parts.Length == 2)
                        {
                            var gameName = parts[0].Trim().Trim('"');
                            var exesString = parts[1].Trim().TrimStart('[').TrimEnd(']').TrimEnd(',');
                            var exes = exesString.Split(',')
                                .Select(e => e.Trim().Trim('"'))
                                .Where(e => !string.IsNullOrEmpty(e))
                                .ToList();

                            if (!string.IsNullOrEmpty(gameName) && exes.Any())
                            {
                                _supportedGames[gameName] = exes;
                            }
                        }
                    }
                }
                Console.WriteLine($"Loaded {_supportedGames.Count} supported games");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading supported games: {ex.Message}");
                _supportedGames = new Dictionary<string, List<string>>();
            }
        }

        private void AddGame_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "Select Game Executable",
                Filter = "Executable files (*.exe)|*.exe|All files (*.*)|*.*",
                CheckFileExists = true
            };

            if (dialog.ShowDialog() == true)
            {
                string exePath = dialog.FileName;
                string exeName = Path.GetFileName(exePath);

                // Check if this is a supported game
                var supportedGame = _supportedGames.FirstOrDefault(kvp =>
                    kvp.Value.Any(exe => exe.Equals(exeName, StringComparison.OrdinalIgnoreCase)));

                if (supportedGame.Key != null)
                {
                    // Supported game
                    AddGameToConfig(supportedGame.Key, exePath, true);
                    MessageBox.Show($"Added supported game: {supportedGame.Key}", "Game Added",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    // Unsupported game - get name from user
                    string gameName = PromptForGameName(Path.GetFileNameWithoutExtension(exeName));

                    if (!string.IsNullOrEmpty(gameName))
                    {
                        AddGameToConfig(gameName, exePath, false);
                        MessageBox.Show($"Added unsupported game: {gameName}\n\nNote: This game is not yet supported by Fennec.",
                            "Game Added", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }

                LoadUserGames();
            }
        }

        private string PromptForGameName(string defaultName)
        {
            // Simple input dialog using a custom window
            var inputDialog = new Window
            {
                Title = "Game Name",
                Width = 400,
                Height = 200,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = this,
                ResizeMode = ResizeMode.NoResize,
                Background = new SolidColorBrush(Color.FromRgb(0x15, 0x15, 0x19))
            };

            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            var label = new TextBlock
            {
                Text = "This game is not yet supported by Fennec.\nPlease enter the game name:",
                Foreground = new SolidColorBrush(Color.FromRgb(0xe4, 0xe5, 0xe6)),
                Margin = new Thickness(20, 20, 20, 10),
                TextWrapping = TextWrapping.Wrap
            };
            Grid.SetRow(label, 0);

            var textBox = new TextBox
            {
                Text = defaultName,
                Margin = new Thickness(20, 10, 20, 20),
                Padding = new Thickness(8),
                Background = new SolidColorBrush(Color.FromRgb(0x22, 0x23, 0x28)),
                Foreground = new SolidColorBrush(Color.FromRgb(0xe4, 0xe5, 0xe6)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(0x33, 0x33, 0x38)),
                BorderThickness = new Thickness(1)
            };
            Grid.SetRow(textBox, 1);

            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(20, 0, 20, 20)
            };

            var okButton = new Button
            {
                Content = "OK",
                Width = 80,
                Height = 30,
                Margin = new Thickness(0, 0, 10, 0),
                Background = new SolidColorBrush(Color.FromRgb(0xfa, 0x00, 0x00)),
                Foreground = new SolidColorBrush(Colors.White),
                BorderThickness = new Thickness(0)
            };

            var cancelButton = new Button
            {
                Content = "Cancel",
                Width = 80,
                Height = 30,
                Background = new SolidColorBrush(Color.FromRgb(0x33, 0x33, 0x38)),
                Foreground = new SolidColorBrush(Color.FromRgb(0xe4, 0xe5, 0xe6)),
                BorderThickness = new Thickness(0)
            };

            string result = null;
            okButton.Click += (s, e) => { result = textBox.Text; inputDialog.Close(); };
            cancelButton.Click += (s, e) => { inputDialog.Close(); };

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);
            Grid.SetRow(buttonPanel, 2);

            grid.Children.Add(label);
            grid.Children.Add(textBox);
            grid.Children.Add(buttonPanel);
            inputDialog.Content = grid;

            textBox.Focus();
            textBox.SelectAll();
            inputDialog.ShowDialog();

            return result;
        }

        private void AddGameToConfig(string gameName, string exePath, bool isSupported)
        {
            try
            {
                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(_gamesConfigPath));

                // Create or append to config file
                var configLines = new List<string>();
                if (File.Exists(_gamesConfigPath))
                {
                    configLines = File.ReadAllLines(_gamesConfigPath).ToList();
                }

                // Add new game entry
                configLines.Add($"[{gameName}]");
                configLines.Add($"name={gameName}");
                configLines.Add($"exe_path={exePath}");
                configLines.Add($"logo_path="); // Will be auto-detected later
                configLines.Add($"supported={isSupported}");
                configLines.Add(""); // Empty line for separation

                File.WriteAllLines(_gamesConfigPath, configLines);
                Console.WriteLine($"Added game to config: {gameName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving game config: {ex.Message}");
                MessageBox.Show($"Error saving game: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadUserGames()
        {
            // Clear existing games
            GamesListPanel.Children.Clear();

            if (!File.Exists(_gamesConfigPath))
                return;

            try
            {
                var lines = File.ReadAllLines(_gamesConfigPath);
                string currentGame = null;
                string gameName = null;
                string exePath = null;
                bool isSupported = false;

                foreach (var line in lines)
                {
                    if (line.StartsWith("[") && line.EndsWith("]"))
                    {
                        // Save previous game if exists
                        if (!string.IsNullOrEmpty(currentGame) && !string.IsNullOrEmpty(gameName))
                        {
                            AddGameToUI(gameName, exePath, isSupported);
                        }

                        // Start new game
                        currentGame = line.Trim('[', ']');
                        gameName = null;
                        exePath = null;
                        isSupported = false;
                    }
                    else if (line.StartsWith("name="))
                    {
                        gameName = line.Substring(5);
                    }
                    else if (line.StartsWith("exe_path="))
                    {
                        exePath = line.Substring(9);
                    }
                    else if (line.StartsWith("supported="))
                    {
                        bool.TryParse(line.Substring(10), out isSupported);
                    }
                }

                // Add last game
                if (!string.IsNullOrEmpty(currentGame) && !string.IsNullOrEmpty(gameName))
                {
                    AddGameToUI(gameName, exePath, isSupported);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading user games: {ex.Message}");
            }
        }

        private void AddGameToUI(string gameName, string exePath, bool isSupported)
        {
            var gameButton = new Button
            {
                Height = 50,
                Margin = new Thickness(0, 2, 0, 2),
                Background = System.Windows.Media.Brushes.Transparent,
                BorderThickness = new Thickness(0),
                Cursor = Cursors.Hand,
                HorizontalAlignment = HorizontalAlignment.Stretch
            };

            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0x33, 0x33, 0x38)),
                CornerRadius = new CornerRadius(6),
                Padding = new Thickness(12, 8, 12, 8)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var textStack = new StackPanel();
            var nameText = new TextBlock
            {
                Text = gameName,
                Foreground = new SolidColorBrush(Color.FromRgb(0xe4, 0xe5, 0xe6)),
                FontSize = 12,
                FontWeight = FontWeights.Medium
            };

            var statusText = new TextBlock
            {
                Text = isSupported ? "Supported" : "Not Supported",
                Foreground = new SolidColorBrush(isSupported ? Color.FromRgb(0x4C, 0xAF, 0x50) : Color.FromRgb(0xFF, 0x98, 0x00)),
                FontSize = 9
            };

            textStack.Children.Add(nameText);
            textStack.Children.Add(statusText);

            Grid.SetColumn(textStack, 0);
            grid.Children.Add(textStack);

            border.Child = grid;
            gameButton.Content = border;

            GamesListPanel.Children.Add(gameButton);
        }


    }
}