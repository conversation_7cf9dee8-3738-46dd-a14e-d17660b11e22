using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using Microsoft.Win32;
using WinForms = System.Windows.Forms;
using DrawingColor = System.Drawing.Color;
using MediaColor = System.Windows.Media.Color;

namespace FennecClient.App
{
    public partial class MainWindow : Window
    {
        private const int WH_KEYBOARD_LL = 13;
        private const int WM_KEYDOWN = 0x0100;
        private const int VK_INSERT = 0x2D;
        
        // Streamproof constants
        private const uint WDA_NONE = 0x00000000;
        private const uint WDA_MONITOR = 0x00000001;
        private const uint WDA_EXCLUDEFROMCAPTURE = 0x00000011;
        
        // Extended window styles
        private const int GWL_EXSTYLE = -20;
        private const uint WS_EX_NOREDIRECTIONBITMAP = 0x00200000;
        
        private delegate IntPtr LowLevelKeyboardProc(int nCode, IntPtr wParam, IntPtr lParam);
        private LowLevelKeyboardProc _proc = HookCallback;
        private static IntPtr _hookID = IntPtr.Zero;
        private static MainWindow _instance;
        private bool _isSidebarVisible = true;
        private Dictionary<string, List<string>> _supportedGames;
        private string _gamesConfigPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Fennec", "user_games.ini");

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr SetWindowsHookEx(int idHook, LowLevelKeyboardProc lpfn, IntPtr hMod, uint dwThreadId);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool UnhookWindowsHookEx(IntPtr hhk);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr CallNextHookEx(IntPtr hhk, int nCode, IntPtr wParam, IntPtr lParam);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("user32.dll")]
        private static extern uint SetWindowDisplayAffinity(IntPtr hwnd, uint dwAffinity);

        [DllImport("user32.dll")]
        private static extern uint GetWindowLong(IntPtr hWnd, int nIndex);

        [DllImport("user32.dll")]
        private static extern uint SetWindowLong(IntPtr hWnd, int nIndex, uint dwNewLong);

        [DllImport("user32.dll")]
        private static extern bool InvalidateRect(IntPtr hWnd, IntPtr lpRect, bool bErase);

        [DllImport("user32.dll")]
        private static extern bool UpdateWindow(IntPtr hWnd);

        public MainWindow()
        {
            try
            {
                InitializeComponent();
                Console.WriteLine("MainWindow initialized successfully");
                
                _instance = this;
                SetupHotkey();
                SetupWindowConstraints();
                SetupLogo();

                // Apply streamproof protection by default and update toggle icons
                this.Loaded += (s, e) => {
                    ApplyStreamproofProtection(true);
                    UpdateToggleIcons();
                    LoadSupportedGames();
                    LoadUserGames();
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing MainWindow: {ex.Message}\n{ex.StackTrace}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyStreamproofProtection(bool enable)
        {
            try
            {
                var hwnd = new WindowInteropHelper(this).Handle;
                if (hwnd != IntPtr.Zero)
                {
                    if (enable)
                    {
                        // Make window completely invisible to screen capture
                        SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE);
                        System.Threading.Thread.Sleep(50); // Small delay
                        
                        // Add extended style to prevent DWM redirection
                        uint exStyle = GetWindowLong(hwnd, GWL_EXSTYLE);
                        SetWindowLong(hwnd, GWL_EXSTYLE, exStyle | WS_EX_NOREDIRECTIONBITMAP);
                        System.Threading.Thread.Sleep(50); // Small delay
                        
                        // Force window update
                        InvalidateRect(hwnd, IntPtr.Zero, true);
                        UpdateWindow(hwnd);
                    }
                    else
                    {
                        // Remove protection
                        SetWindowDisplayAffinity(hwnd, WDA_NONE);
                        System.Threading.Thread.Sleep(50); // Small delay
                        
                        // Remove extended style
                        uint exStyle = GetWindowLong(hwnd, GWL_EXSTYLE);
                        SetWindowLong(hwnd, GWL_EXSTYLE, exStyle & ~WS_EX_NOREDIRECTIONBITMAP);
                        System.Threading.Thread.Sleep(50); // Small delay
                        
                        // Force window update
                        InvalidateRect(hwnd, IntPtr.Zero, true);
                        UpdateWindow(hwnd);
                    }
                    
                    // Also protect overlay windows if they exist
                    foreach (Window window in System.Windows.Application.Current.Windows)
                    {
                        if (window != this && window.GetType().Name.Contains("Overlay"))
                        {
                            var overlayHwnd = new WindowInteropHelper(window).Handle;
                            if (overlayHwnd != IntPtr.Zero)
                            {
                                if (enable)
                                {
                                    SetWindowDisplayAffinity(overlayHwnd, WDA_EXCLUDEFROMCAPTURE);
                                    System.Threading.Thread.Sleep(25);
                                    uint overlayExStyle = GetWindowLong(overlayHwnd, GWL_EXSTYLE);
                                    SetWindowLong(overlayHwnd, GWL_EXSTYLE, overlayExStyle | WS_EX_NOREDIRECTIONBITMAP);
                                    System.Threading.Thread.Sleep(25);
                                    InvalidateRect(overlayHwnd, IntPtr.Zero, true);
                                    UpdateWindow(overlayHwnd);
                                }
                                else
                                {
                                    SetWindowDisplayAffinity(overlayHwnd, WDA_NONE);
                                    System.Threading.Thread.Sleep(25);
                                    uint overlayExStyle = GetWindowLong(overlayHwnd, GWL_EXSTYLE);
                                    SetWindowLong(overlayHwnd, GWL_EXSTYLE, overlayExStyle & ~WS_EX_NOREDIRECTIONBITMAP);
                                    System.Threading.Thread.Sleep(25);
                                    InvalidateRect(overlayHwnd, IntPtr.Zero, true);
                                    UpdateWindow(overlayHwnd);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error applying streamproof protection: {ex.Message}");
            }
        }



        private void SetupLogo()
        {
            // Handle title bar logo loading failure
            TitleBarLogoImage.ImageFailed += (s, e) => {
                TitleBarLogoImage.Visibility = Visibility.Collapsed;
                TitleBarLogoFallback.Visibility = Visibility.Visible;
            };
        }

        private void SetupHotkey()
        {
            _hookID = SetHook(_proc);
        }

        private void SetupWindowConstraints()
        {
            // Get primary screen bounds
            var primaryScreen = WinForms.Screen.PrimaryScreen;
            
            // Set window constraints to stay within screen bounds with margins
            this.MaxWidth = primaryScreen.Bounds.Width - 40; // 20px margin on each side
            this.MaxHeight = primaryScreen.Bounds.Height - 40; // 20px margin on top/bottom
            
            // Constrain window movement
            this.LocationChanged += MainWindow_LocationChanged;
        }

        private void MainWindow_LocationChanged(object sender, EventArgs e)
        {
            var primaryScreen = WinForms.Screen.PrimaryScreen;
            var margin = 20;
            
            // Ensure window stays within screen bounds
            if (this.Left < margin)
                this.Left = margin;
            
            if (this.Top < margin)
                this.Top = margin;
                
            if (this.Left + this.Width > primaryScreen.Bounds.Width - margin)
                this.Left = primaryScreen.Bounds.Width - this.Width - margin;
                
            if (this.Top + this.Height > primaryScreen.Bounds.Height - margin)
                this.Top = primaryScreen.Bounds.Height - this.Height - margin;
        }

        private static IntPtr SetHook(LowLevelKeyboardProc proc)
        {
            using (Process curProcess = Process.GetCurrentProcess())
            using (ProcessModule curModule = curProcess.MainModule)
            {
                return SetWindowsHookEx(WH_KEYBOARD_LL, proc,
                    GetModuleHandle(curModule.ModuleName), 0);
            }
        }

        private static IntPtr HookCallback(int nCode, IntPtr wParam, IntPtr lParam)
        {
            if (nCode >= 0 && wParam == (IntPtr)WM_KEYDOWN)
            {
                int vkCode = Marshal.ReadInt32(lParam);
                if (vkCode == VK_INSERT)
                {
                    // Toggle window visibility
                    _instance?.Dispatcher.Invoke(() => {
                        if (_instance.Visibility == Visibility.Visible)
                        {
                            _instance.Hide();
                            Console.WriteLine("MainWindow hidden with INSERT key");
                        }
                        else
                        {
                            _instance.Show();
                            _instance.Activate();
                            Console.WriteLine("MainWindow shown with INSERT key");
                        }
                    });
                }
            }
            return CallNextHookEx(_hookID, nCode, wParam, lParam);
        }

        protected override void OnClosed(EventArgs e)
        {
            UnhookWindowsHookEx(_hookID);
            base.OnClosed(e);
        }



        // Window Controls
        private void TitleBar_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            DragMove();
        }

        private void MinimizeWindow_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void MaximizeWindow_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
        }

        private void CloseWindow_Click(object sender, RoutedEventArgs e)
        {
            Console.WriteLine("Application closing...");
            Console.WriteLine("Press any key to close console...");
            this.Close();
        }

        private void ToggleSidebar_Click(object sender, RoutedEventArgs e)
        {
            _isSidebarVisible = !_isSidebarVisible;

            if (_isSidebarVisible)
            {
                // Expand sidebar to normal size
                SidebarColumn.Width = new GridLength(220);
                SidebarColumn.MinWidth = 180;
                SidebarColumn.MaxWidth = 400;
                SplitterColumn.Width = new GridLength(5);
            }
            else
            {
                // Collapse sidebar to minimal size (just show a thin strip)
                SidebarColumn.Width = new GridLength(40);
                SidebarColumn.MinWidth = 40;
                SidebarColumn.MaxWidth = 40;
                SplitterColumn.Width = new GridLength(0);
            }

            // Update toggle icons in both hamburger menu and sidebar button
            UpdateToggleIcons();

            Console.WriteLine($"Sidebar toggled: {(_isSidebarVisible ? "Expanded" : "Collapsed")}");
        }

        private void UpdateToggleIcons()
        {
            // Find the toggle icon in the sidebar button and update it
            if (SidebarToggleButton?.Template != null)
            {
                var toggleIcon = SidebarToggleButton.Template.FindName("ToggleIcon", SidebarToggleButton) as TextBlock;
                if (toggleIcon != null)
                {
                    // Use different icons based on state
                    // &#xE76B; = ChevronLeft (collapse), &#xE76C; = ChevronRight (expand)
                    toggleIcon.Text = _isSidebarVisible ? "\uE76B" : "\uE76C";
                }
            }
        }

        private void LoadSupportedGames()
        {
            _supportedGames = new Dictionary<string, List<string>>();
            string gamesIniPath = @"c:\Users\<USER>\Desktop\Fennec\games.ini";

            try
            {
                if (File.Exists(gamesIniPath))
                {
                    var lines = File.ReadAllLines(gamesIniPath);
                    foreach (var line in lines)
                    {
                        if (string.IsNullOrWhiteSpace(line)) continue;

                        // Parse format: "Game Name": ["exe1.exe", "exe2.exe"]
                        var colonIndex = line.IndexOf(':');
                        if (colonIndex > 0)
                        {
                            var gameName = line.Substring(0, colonIndex).Trim().Trim('"');
                            var exesString = line.Substring(colonIndex + 1).Trim();

                            // Clean up the executables string - remove all brackets, quotes, and trailing characters
                            exesString = exesString.Replace("[", "").Replace("]", "").Replace("\"", "").TrimEnd(',');
                            var exes = exesString.Split(',')
                                .Select(e => e.Trim())
                                .Where(e => !string.IsNullOrEmpty(e))
                                .ToList();

                            if (!string.IsNullOrEmpty(gameName) && exes.Any())
                            {
                                _supportedGames[gameName] = exes;
                                Console.WriteLine($"Loaded game: {gameName} with executables: {string.Join(", ", exes)}");
                            }
                        }
                    }
                }
                Console.WriteLine($"Loaded {_supportedGames.Count} supported games");

                // Debug: Print all loaded games
                foreach (var game in _supportedGames)
                {
                    Console.WriteLine($"Game: {game.Key} -> {string.Join(", ", game.Value)}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading supported games: {ex.Message}");
                _supportedGames = new Dictionary<string, List<string>>();
            }
        }

        private void AddGame_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "Select Game Executable",
                Filter = "Executable files (*.exe)|*.exe|All files (*.*)|*.*",
                CheckFileExists = true
            };

            if (dialog.ShowDialog() == true)
            {
                string exePath = dialog.FileName;
                string exeName = Path.GetFileName(exePath);

                // Check if this is a supported game
                var supportedGame = _supportedGames.FirstOrDefault(kvp =>
                    kvp.Value.Any(exe => exe.Equals(exeName, StringComparison.OrdinalIgnoreCase)));

                if (supportedGame.Key != null)
                {
                    // Supported game - find logo automatically
                    string logoPath = FindGameLogo(exePath, supportedGame.Key);
                    AddGameToConfig(supportedGame.Key, exePath, logoPath, true);
                    MessageBox.Show($"Added supported game: {supportedGame.Key}\nLogo: {(string.IsNullOrEmpty(logoPath) ? "Not found" : "Found")}",
                        "Game Added", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    // Unsupported game - get name from user and find logo
                    string gameName = PromptForGameName(Path.GetFileNameWithoutExtension(exeName));

                    if (!string.IsNullOrEmpty(gameName))
                    {
                        string logoPath = FindGameLogo(exePath, gameName);
                        AddGameToConfig(gameName, exePath, logoPath, false);
                        MessageBox.Show($"Added unsupported game: {gameName}\nLogo: {(string.IsNullOrEmpty(logoPath) ? "Not found" : "Found")}\n\nNote: This game is not yet supported by Fennec.",
                            "Game Added", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }

                LoadUserGames();
            }
        }

        private string PromptForGameName(string defaultName)
        {
            // Simple input dialog using a custom window
            var inputDialog = new Window
            {
                Title = "Game Name",
                Width = 400,
                Height = 200,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = this,
                ResizeMode = ResizeMode.NoResize,
                Background = new SolidColorBrush(MediaColor.FromRgb(0x15, 0x15, 0x19))
            };

            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            var label = new TextBlock
            {
                Text = "This game is not yet supported by Fennec.\nPlease enter the game name:",
                Foreground = new SolidColorBrush(MediaColor.FromRgb(0xe4, 0xe5, 0xe6)),
                Margin = new Thickness(20, 20, 20, 10),
                TextWrapping = TextWrapping.Wrap
            };
            Grid.SetRow(label, 0);

            var textBox = new TextBox
            {
                Text = defaultName,
                Margin = new Thickness(20, 10, 20, 20),
                Padding = new Thickness(8),
                Background = new SolidColorBrush(MediaColor.FromRgb(0x22, 0x23, 0x28)),
                Foreground = new SolidColorBrush(MediaColor.FromRgb(0xe4, 0xe5, 0xe6)),
                BorderBrush = new SolidColorBrush(MediaColor.FromRgb(0x33, 0x33, 0x38)),
                BorderThickness = new Thickness(1)
            };
            Grid.SetRow(textBox, 1);

            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(20, 0, 20, 20)
            };

            var okButton = new Button
            {
                Content = "OK",
                Width = 80,
                Height = 30,
                Margin = new Thickness(0, 0, 10, 0),
                Background = new SolidColorBrush(MediaColor.FromRgb(0xfa, 0x00, 0x00)),
                Foreground = new SolidColorBrush(Colors.White),
                BorderThickness = new Thickness(0)
            };

            var cancelButton = new Button
            {
                Content = "Cancel",
                Width = 80,
                Height = 30,
                Background = new SolidColorBrush(MediaColor.FromRgb(0x33, 0x33, 0x38)),
                Foreground = new SolidColorBrush(MediaColor.FromRgb(0xe4, 0xe5, 0xe6)),
                BorderThickness = new Thickness(0)
            };

            string result = null;
            okButton.Click += (s, e) => { result = textBox.Text; inputDialog.Close(); };
            cancelButton.Click += (s, e) => { inputDialog.Close(); };

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);
            Grid.SetRow(buttonPanel, 2);

            grid.Children.Add(label);
            grid.Children.Add(textBox);
            grid.Children.Add(buttonPanel);
            inputDialog.Content = grid;

            textBox.Focus();
            textBox.SelectAll();
            inputDialog.ShowDialog();

            return result;
        }

        private string FindGameLogo(string exePath, string gameName)
        {
            try
            {
                string gameDirectory = Path.GetDirectoryName(exePath);
                string logoPath = null;

                Console.WriteLine($"Searching for logo for {gameName} in {gameDirectory}");

                // 1. Try to extract icon from the executable itself
                logoPath = ExtractExeIcon(exePath, gameName);
                if (!string.IsNullOrEmpty(logoPath))
                {
                    Console.WriteLine($"Found exe icon: {logoPath}");
                    return logoPath;
                }

                // 2. Look for common logo file names in the game directory
                var commonLogoNames = new[]
                {
                    "logo.png", "logo.jpg", "logo.ico", "icon.png", "icon.jpg", "icon.ico",
                    "game_logo.png", "game_icon.png", "app_icon.png", "app_logo.png",
                    $"{gameName.Replace(" ", "").ToLower()}.png",
                    $"{gameName.Replace(" ", "").ToLower()}.jpg",
                    $"{gameName.Replace(" ", "").ToLower()}.ico",
                    $"{Path.GetFileNameWithoutExtension(exePath)}.png",
                    $"{Path.GetFileNameWithoutExtension(exePath)}.jpg",
                    $"{Path.GetFileNameWithoutExtension(exePath)}.ico"
                };

                foreach (var logoName in commonLogoNames)
                {
                    var fullPath = Path.Combine(gameDirectory, logoName);
                    if (File.Exists(fullPath))
                    {
                        Console.WriteLine($"Found logo file: {fullPath}");
                        return fullPath;
                    }
                }

                // 3. Look in common subdirectories
                var commonSubdirs = new[] { "images", "icons", "assets", "resources", "data", "ui", "gfx" };
                foreach (var subdir in commonSubdirs)
                {
                    var subdirPath = Path.Combine(gameDirectory, subdir);
                    if (Directory.Exists(subdirPath))
                    {
                        foreach (var logoName in commonLogoNames)
                        {
                            var fullPath = Path.Combine(subdirPath, logoName);
                            if (File.Exists(fullPath))
                            {
                                Console.WriteLine($"Found logo in subdir: {fullPath}");
                                return fullPath;
                            }
                        }
                    }
                }

                // 4. Look for any image files that might be logos (first few found)
                var imageExtensions = new[] { "*.png", "*.jpg", "*.jpeg", "*.ico", "*.bmp" };
                foreach (var extension in imageExtensions)
                {
                    var imageFiles = Directory.GetFiles(gameDirectory, extension, SearchOption.TopDirectoryOnly);
                    foreach (var imageFile in imageFiles.Take(3)) // Only check first 3 to avoid performance issues
                    {
                        var fileName = Path.GetFileNameWithoutExtension(imageFile).ToLower();
                        if (fileName.Contains("logo") || fileName.Contains("icon") || fileName.Contains(gameName.Replace(" ", "").ToLower()))
                        {
                            Console.WriteLine($"Found potential logo: {imageFile}");
                            return imageFile;
                        }
                    }
                }

                Console.WriteLine($"No logo found for {gameName}");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error finding logo for {gameName}: {ex.Message}");
                return null;
            }
        }

        private string ExtractExeIcon(string exePath, string gameName)
        {
            try
            {
                // Create a directory for extracted icons
                string iconsDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Fennec", "icons");
                Directory.CreateDirectory(iconsDir);

                string iconPath = Path.Combine(iconsDir, $"{gameName.Replace(" ", "_").Replace(":", "")}.png");

                // Extract icon from exe
                System.Drawing.Icon icon = System.Drawing.Icon.ExtractAssociatedIcon(exePath);
                if (icon != null)
                {
                    // Convert icon to bitmap and save as PNG
                    using (System.Drawing.Bitmap bitmap = icon.ToBitmap())
                    {
                        bitmap.Save(iconPath, System.Drawing.Imaging.ImageFormat.Png);
                    }
                    icon.Dispose();

                    Console.WriteLine($"Extracted icon from exe: {iconPath}");
                    return iconPath;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error extracting icon from {exePath}: {ex.Message}");
            }

            return null;
        }

        private void AddGameToConfig(string gameName, string exePath, string logoPath, bool isSupported)
        {
            try
            {
                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(_gamesConfigPath));

                // Create or append to config file
                var configLines = new List<string>();
                if (File.Exists(_gamesConfigPath))
                {
                    configLines = File.ReadAllLines(_gamesConfigPath).ToList();
                }

                // Add new game entry
                configLines.Add($"[{gameName}]");
                configLines.Add($"name={gameName}");
                configLines.Add($"exe_path={exePath}");
                configLines.Add($"logo_path={logoPath ?? ""}");
                configLines.Add($"supported={isSupported}");
                configLines.Add(""); // Empty line for separation

                File.WriteAllLines(_gamesConfigPath, configLines);
                Console.WriteLine($"Added game to config: {gameName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving game config: {ex.Message}");
                MessageBox.Show($"Error saving game: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadUserGames()
        {
            // Clear existing games
            GamesListPanel.Children.Clear();

            if (!File.Exists(_gamesConfigPath))
                return;

            try
            {
                var lines = File.ReadAllLines(_gamesConfigPath);
                string currentGame = null;
                string gameName = null;
                string exePath = null;
                string logoPath = null;
                bool isSupported = false;

                foreach (var line in lines)
                {
                    if (line.StartsWith("[") && line.EndsWith("]"))
                    {
                        // Save previous game if exists
                        if (!string.IsNullOrEmpty(currentGame) && !string.IsNullOrEmpty(gameName))
                        {
                            AddGameToUI(gameName, exePath, logoPath, isSupported);
                        }

                        // Start new game
                        currentGame = line.Trim('[', ']');
                        gameName = null;
                        exePath = null;
                        logoPath = null;
                        isSupported = false;
                    }
                    else if (line.StartsWith("name="))
                    {
                        gameName = line.Substring(5);
                    }
                    else if (line.StartsWith("exe_path="))
                    {
                        exePath = line.Substring(9);
                    }
                    else if (line.StartsWith("logo_path="))
                    {
                        logoPath = line.Substring(10);
                    }
                    else if (line.StartsWith("supported="))
                    {
                        bool.TryParse(line.Substring(10), out isSupported);
                    }
                }

                // Add last game
                if (!string.IsNullOrEmpty(currentGame) && !string.IsNullOrEmpty(gameName))
                {
                    AddGameToUI(gameName, exePath, logoPath, isSupported);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading user games: {ex.Message}");
            }
        }

        private void AddGameToUI(string gameName, string exePath, string logoPath, bool isSupported)
        {
            var gameButton = new Button
            {
                Height = 50,
                Margin = new Thickness(0, 2, 0, 2),
                Background = System.Windows.Media.Brushes.Transparent,
                BorderThickness = new Thickness(0),
                Cursor = Cursors.Hand,
                HorizontalAlignment = HorizontalAlignment.Stretch
            };

            var border = new Border
            {
                Background = new SolidColorBrush(MediaColor.FromRgb(0x33, 0x33, 0x38)),
                CornerRadius = new CornerRadius(6),
                Padding = new Thickness(12, 8, 12, 8)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto }); // Logo column
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // Text column

            // Logo/Icon
            var logoImage = new System.Windows.Controls.Image
            {
                Width = 32,
                Height = 32,
                Margin = new Thickness(0, 0, 12, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            // Try to load the logo
            if (!string.IsNullOrEmpty(logoPath) && File.Exists(logoPath))
            {
                try
                {
                    var bitmap = new System.Windows.Media.Imaging.BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(logoPath);
                    bitmap.DecodePixelWidth = 32;
                    bitmap.DecodePixelHeight = 32;
                    bitmap.EndInit();
                    logoImage.Source = bitmap;
                }
                catch
                {
                    // Fallback to default icon
                    logoImage.Source = null;
                }
            }

            // If no logo, show a default game icon
            if (logoImage.Source == null)
            {
                var defaultIcon = new TextBlock
                {
                    Text = "🎮",
                    FontSize = 20,
                    Width = 32,
                    Height = 32,
                    TextAlignment = TextAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(0, 0, 12, 0)
                };
                Grid.SetColumn(defaultIcon, 0);
                grid.Children.Add(defaultIcon);
            }
            else
            {
                Grid.SetColumn(logoImage, 0);
                grid.Children.Add(logoImage);
            }

            var textStack = new StackPanel();
            var nameText = new TextBlock
            {
                Text = gameName,
                Foreground = new SolidColorBrush(MediaColor.FromRgb(0xe4, 0xe5, 0xe6)),
                FontSize = 12,
                FontWeight = FontWeights.Medium
            };

            var statusText = new TextBlock
            {
                Text = isSupported ? "Supported" : "Not Supported",
                Foreground = new SolidColorBrush(isSupported ? MediaColor.FromRgb(0x4C, 0xAF, 0x50) : MediaColor.FromRgb(0xFF, 0x98, 0x00)),
                FontSize = 9
            };

            textStack.Children.Add(nameText);
            textStack.Children.Add(statusText);

            Grid.SetColumn(textStack, 1);
            grid.Children.Add(textStack);

            border.Child = grid;
            gameButton.Content = border;

            GamesListPanel.Children.Add(gameButton);
        }


    }
}