using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using WinForms = System.Windows.Forms;

namespace FennecClient.App
{
    public partial class MainWindow : Window
    {
        private const int WH_KEYBOARD_LL = 13;
        private const int WM_KEYDOWN = 0x0100;
        private const int VK_INSERT = 0x2D;
        
        // Streamproof constants
        private const uint WDA_NONE = 0x00000000;
        private const uint WDA_MONITOR = 0x00000001;
        private const uint WDA_EXCLUDEFROMCAPTURE = 0x00000011;
        
        // Extended window styles
        private const int GWL_EXSTYLE = -20;
        private const uint WS_EX_NOREDIRECTIONBITMAP = 0x00200000;
        
        private delegate IntPtr LowLevelKeyboardProc(int nCode, IntPtr wParam, IntPtr lParam);
        private LowLevelKeyboardProc _proc = HookCallback;
        private static IntPtr _hookID = IntPtr.Zero;
        private static MainWindow _instance;
        private bool _isSidebarVisible = true;

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr SetWindowsHookEx(int idHook, LowLevelKeyboardProc lpfn, IntPtr hMod, uint dwThreadId);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool UnhookWindowsHookEx(IntPtr hhk);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr CallNextHookEx(IntPtr hhk, int nCode, IntPtr wParam, IntPtr lParam);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("user32.dll")]
        private static extern uint SetWindowDisplayAffinity(IntPtr hwnd, uint dwAffinity);

        [DllImport("user32.dll")]
        private static extern uint GetWindowLong(IntPtr hWnd, int nIndex);

        [DllImport("user32.dll")]
        private static extern uint SetWindowLong(IntPtr hWnd, int nIndex, uint dwNewLong);

        [DllImport("user32.dll")]
        private static extern bool InvalidateRect(IntPtr hWnd, IntPtr lpRect, bool bErase);

        [DllImport("user32.dll")]
        private static extern bool UpdateWindow(IntPtr hWnd);

        public MainWindow()
        {
            try
            {
                InitializeComponent();
                Console.WriteLine("MainWindow initialized successfully");
                
                _instance = this;
                SetupHotkey();
                SetupWindowConstraints();
                SetupLogo();
                
                // Apply streamproof protection by default
                this.Loaded += (s, e) => ApplyStreamproofProtection(true);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing MainWindow: {ex.Message}\n{ex.StackTrace}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyStreamproofProtection(bool enable)
        {
            try
            {
                var hwnd = new WindowInteropHelper(this).Handle;
                if (hwnd != IntPtr.Zero)
                {
                    if (enable)
                    {
                        // Make window completely invisible to screen capture
                        SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE);
                        System.Threading.Thread.Sleep(50); // Small delay
                        
                        // Add extended style to prevent DWM redirection
                        uint exStyle = GetWindowLong(hwnd, GWL_EXSTYLE);
                        SetWindowLong(hwnd, GWL_EXSTYLE, exStyle | WS_EX_NOREDIRECTIONBITMAP);
                        System.Threading.Thread.Sleep(50); // Small delay
                        
                        // Force window update
                        InvalidateRect(hwnd, IntPtr.Zero, true);
                        UpdateWindow(hwnd);
                    }
                    else
                    {
                        // Remove protection
                        SetWindowDisplayAffinity(hwnd, WDA_NONE);
                        System.Threading.Thread.Sleep(50); // Small delay
                        
                        // Remove extended style
                        uint exStyle = GetWindowLong(hwnd, GWL_EXSTYLE);
                        SetWindowLong(hwnd, GWL_EXSTYLE, exStyle & ~WS_EX_NOREDIRECTIONBITMAP);
                        System.Threading.Thread.Sleep(50); // Small delay
                        
                        // Force window update
                        InvalidateRect(hwnd, IntPtr.Zero, true);
                        UpdateWindow(hwnd);
                    }
                    
                    // Also protect overlay windows if they exist
                    foreach (Window window in System.Windows.Application.Current.Windows)
                    {
                        if (window != this && window.GetType().Name.Contains("Overlay"))
                        {
                            var overlayHwnd = new WindowInteropHelper(window).Handle;
                            if (overlayHwnd != IntPtr.Zero)
                            {
                                if (enable)
                                {
                                    SetWindowDisplayAffinity(overlayHwnd, WDA_EXCLUDEFROMCAPTURE);
                                    System.Threading.Thread.Sleep(25);
                                    uint overlayExStyle = GetWindowLong(overlayHwnd, GWL_EXSTYLE);
                                    SetWindowLong(overlayHwnd, GWL_EXSTYLE, overlayExStyle | WS_EX_NOREDIRECTIONBITMAP);
                                    System.Threading.Thread.Sleep(25);
                                    InvalidateRect(overlayHwnd, IntPtr.Zero, true);
                                    UpdateWindow(overlayHwnd);
                                }
                                else
                                {
                                    SetWindowDisplayAffinity(overlayHwnd, WDA_NONE);
                                    System.Threading.Thread.Sleep(25);
                                    uint overlayExStyle = GetWindowLong(overlayHwnd, GWL_EXSTYLE);
                                    SetWindowLong(overlayHwnd, GWL_EXSTYLE, overlayExStyle & ~WS_EX_NOREDIRECTIONBITMAP);
                                    System.Threading.Thread.Sleep(25);
                                    InvalidateRect(overlayHwnd, IntPtr.Zero, true);
                                    UpdateWindow(overlayHwnd);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error applying streamproof protection: {ex.Message}");
            }
        }



        private void SetupLogo()
        {
            // Handle title bar logo loading failure
            TitleBarLogoImage.ImageFailed += (s, e) => {
                TitleBarLogoImage.Visibility = Visibility.Collapsed;
                TitleBarLogoFallback.Visibility = Visibility.Visible;
            };
        }

        private void SetupHotkey()
        {
            _hookID = SetHook(_proc);
        }

        private void SetupWindowConstraints()
        {
            // Get primary screen bounds
            var primaryScreen = WinForms.Screen.PrimaryScreen;
            
            // Set window constraints to stay within screen bounds with margins
            this.MaxWidth = primaryScreen.Bounds.Width - 40; // 20px margin on each side
            this.MaxHeight = primaryScreen.Bounds.Height - 40; // 20px margin on top/bottom
            
            // Constrain window movement
            this.LocationChanged += MainWindow_LocationChanged;
        }

        private void MainWindow_LocationChanged(object sender, EventArgs e)
        {
            var primaryScreen = WinForms.Screen.PrimaryScreen;
            var margin = 20;
            
            // Ensure window stays within screen bounds
            if (this.Left < margin)
                this.Left = margin;
            
            if (this.Top < margin)
                this.Top = margin;
                
            if (this.Left + this.Width > primaryScreen.Bounds.Width - margin)
                this.Left = primaryScreen.Bounds.Width - this.Width - margin;
                
            if (this.Top + this.Height > primaryScreen.Bounds.Height - margin)
                this.Top = primaryScreen.Bounds.Height - this.Height - margin;
        }

        private static IntPtr SetHook(LowLevelKeyboardProc proc)
        {
            using (Process curProcess = Process.GetCurrentProcess())
            using (ProcessModule curModule = curProcess.MainModule)
            {
                return SetWindowsHookEx(WH_KEYBOARD_LL, proc,
                    GetModuleHandle(curModule.ModuleName), 0);
            }
        }

        private static IntPtr HookCallback(int nCode, IntPtr wParam, IntPtr lParam)
        {
            if (nCode >= 0 && wParam == (IntPtr)WM_KEYDOWN)
            {
                int vkCode = Marshal.ReadInt32(lParam);
                if (vkCode == VK_INSERT)
                {
                    // Toggle window visibility
                    _instance?.Dispatcher.Invoke(() => {
                        if (_instance.Visibility == Visibility.Visible)
                        {
                            _instance.Hide();
                            Console.WriteLine("MainWindow hidden with INSERT key");
                        }
                        else
                        {
                            _instance.Show();
                            _instance.Activate();
                            Console.WriteLine("MainWindow shown with INSERT key");
                        }
                    });
                }
            }
            return CallNextHookEx(_hookID, nCode, wParam, lParam);
        }

        protected override void OnClosed(EventArgs e)
        {
            UnhookWindowsHookEx(_hookID);
            base.OnClosed(e);
        }



        // Window Controls
        private void TitleBar_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            DragMove();
        }

        private void MinimizeWindow_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void MaximizeWindow_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
        }

        private void CloseWindow_Click(object sender, RoutedEventArgs e)
        {
            Console.WriteLine("Application closing...");
            Console.WriteLine("Press any key to close console...");
            this.Close();
        }

        private void ToggleSidebar_Click(object sender, RoutedEventArgs e)
        {
            _isSidebarVisible = !_isSidebarVisible;

            if (_isSidebarVisible)
            {
                // Expand sidebar to normal size
                SidebarColumn.Width = new GridLength(220);
                SidebarColumn.MinWidth = 180;
                SidebarColumn.MaxWidth = 400;
                SplitterColumn.Width = new GridLength(5);
            }
            else
            {
                // Collapse sidebar to minimal size (just show a thin strip)
                SidebarColumn.Width = new GridLength(40);
                SidebarColumn.MinWidth = 40;
                SidebarColumn.MaxWidth = 40;
                SplitterColumn.Width = new GridLength(0);
            }

            Console.WriteLine($"Sidebar toggled: {(_isSidebarVisible ? "Expanded" : "Collapsed")}");
        }


    }
} 