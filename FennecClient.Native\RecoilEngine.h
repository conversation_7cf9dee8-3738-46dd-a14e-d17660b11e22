#pragma once
#include <Windows.h>
#include <memory>
#include <vector>
#include <mutex>
#include <thread>
#include <atomic>
#include <chrono>
#include <set>
#include <string>
#include <algorithm>
#include <random>
#include <cmath>
#include <iostream>

#ifdef FENNECCLIENTNATIVE_EXPORTS
#define RECOIL_API __declspec(dllexport)
#else
#define RECOIL_API __declspec(dllimport)
#endif

// Recoil configuration structure
struct RecoilConfig
{
    double horizontalStrength;
    double verticalStrength;
    double sensitivity;
    double delayMs;
    int activationKey; // VK_LBUTTON, VK_RBUTTON, etc.
    bool enabled;
    bool useRandomization;
    double randomizationFactor; // 0.0 to 1.0
    double smoothness; // 0.0 to 1.0 for smoothing between recoil steps
};

// Recoil statistics for debugging
struct RecoilStats
{
    uint64_t totalCompensations;
    uint64_t totalMouseMovements;
    double averageLatencyMs;
    double maxLatencyMs;
    uint64_t missedFrames;
    bool inGameWindow;
    char currentGame[256];
};

extern "C" {
    // Core engine functions
    RECOIL_API bool InitializeRecoilEngine();
    RECOIL_API void ShutdownRecoilEngine();
    
    // Configuration
    RECOIL_API bool SetRecoilConfig(const RecoilConfig* config);
    RECOIL_API bool GetRecoilConfig(RecoilConfig* config);
    
    // Control
    RECOIL_API bool StartRecoilCompensation();
    RECOIL_API bool StopRecoilCompensation();
    RECOIL_API bool IsRecoilActive();
    
    // Statistics and debugging
    RECOIL_API bool GetRecoilStats(RecoilStats* stats);
    RECOIL_API void ResetRecoilStats();
    
    // Advanced features
    RECOIL_API bool SetRecoilPattern(const double* horizontalPattern, const double* verticalPattern, int patternLength);
    RECOIL_API bool EnableAdaptiveRecoil(bool enable);
    
    // Game detection
    RECOIL_API bool AddSupportedGame(const char* executableName);
    RECOIL_API bool RemoveSupportedGame(const char* executableName);
    RECOIL_API bool IsInSupportedGame();
}

// Internal classes (not exported)
class RecoilEngine
{
public:
    RecoilEngine();
    ~RecoilEngine();
    
    bool Initialize();
    void Shutdown();
    
    bool SetConfig(const RecoilConfig& config);
    RecoilConfig GetConfig() const;
    
    bool Start();
    bool Stop();
    bool IsActive() const;
    
    RecoilStats GetStats() const;
    void ResetStats();
    
    bool SetPattern(const std::vector<double>& horizontal, const std::vector<double>& vertical);
    void EnableAdaptive(bool enable);
    
    // Game detection
    bool AddSupportedGame(const std::string& executableName);
    bool RemoveSupportedGame(const std::string& executableName);
    bool IsInSupportedGame();

private:
    void RecoilThread();
    void UpdateRecoil();
    bool InjectMouseMovement(int deltaX, int deltaY);
    bool IsActivationKeyPressed() const;
    bool CheckGameWindow();
    
    // Recoil calculation (based on Hermes implementation)
    void CalculateNextRecoilValues(float& x, float& y);
    void ApplyRecoilCompensation(float x, float y);
    void ResetRecoilState();
    
    // High-precision timing
    std::chrono::high_resolution_clock::time_point GetHighResTime() const;
    double GetElapsedMs(const std::chrono::high_resolution_clock::time_point& start) const;
    
    // Thread management
    std::unique_ptr<std::thread> m_recoilThread;
    std::atomic<bool> m_running;
    std::atomic<bool> m_active;
    
    // Configuration
    RecoilConfig m_config;
    mutable std::mutex m_configMutex;
    
    // Pattern support
    std::vector<double> m_horizontalPattern;
    std::vector<double> m_verticalPattern;
    size_t m_patternIndex;
    bool m_usePattern;
    bool m_adaptiveEnabled;
    
    // Statistics
    mutable std::mutex m_statsMutex;
    RecoilStats m_stats;
    
    // Timing and state (based on Hermes)
    std::chrono::high_resolution_clock::time_point m_startTime;
    std::chrono::high_resolution_clock::time_point m_lastUpdateTime;
    int m_currentStep;
    float m_currentX;
    float m_currentY;
    bool m_recoilActive;
    bool m_ignoreNextMouseMove;
    
    // Game detection
    std::set<std::string> m_supportedGames;
    mutable std::mutex m_gamesMutex;
    HWND m_targetWindow;
    std::string m_currentGame;
    
    // Static member for singleton access
    static RecoilEngine* s_instance;
};
