#include "RecoilEngine.h"
#include <TlHelp32.h>
#include <Psapi.h>

// Global engine instance
static std::unique_ptr<RecoilEngine> g_recoilEngine;
static std::mutex g_engineMutex;

// Static member initialization
RecoilEngine* RecoilEngine::s_instance = nullptr;

// Default supported games (based on Hermes)
static const std::set<std::string> DEFAULT_SUPPORTED_GAMES = {
    "r5apex.exe", "r5apex_dx12.exe",                    // Apex Legends
    "escapefromtarkov.exe",                              // Escape from Tarkov
    "fortniteclient-win64-shipping.exe",                // Fortnite
    "fortniteclient-win64-shipping_eac_eos.exe",        // Fortnite EAC
    "modernwarfare.exe",                                 // Modern Warfare 2019
    "tslgame.exe",                                       // PUBG
    "rainbowsix.exe", "rainbowsix_be.exe", "rainbowsix_vulkan.exe", // Rainbow Six Siege
    "rustclient.exe",                                    // Rust
    "cod.exe",                                           // Black Ops 6
    "cod22-cod.exe",                                     // Modern Warfare II
    "cod23-cod.exe",                                     // Modern Warfare III
    "cs2.exe",                                           // Counter-Strike 2
    "deltaforceclient-win64-shipping.exe"               // Delta Force
};

// C API Implementation
extern "C" {

RECOIL_API bool InitializeRecoilEngine()
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (g_recoilEngine) {
        return true; // Already initialized
    }
    
    try {
        g_recoilEngine = std::make_unique<RecoilEngine>();
        return g_recoilEngine->Initialize();
    }
    catch (...) {
        g_recoilEngine.reset();
        return false;
    }
}

RECOIL_API void ShutdownRecoilEngine()
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (g_recoilEngine) {
        g_recoilEngine->Shutdown();
        g_recoilEngine.reset();
    }
}

RECOIL_API bool SetRecoilConfig(const RecoilConfig* config)
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (!g_recoilEngine || !config) {
        return false;
    }
    
    return g_recoilEngine->SetConfig(*config);
}

RECOIL_API bool GetRecoilConfig(RecoilConfig* config)
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (!g_recoilEngine || !config) {
        return false;
    }
    
    *config = g_recoilEngine->GetConfig();
    return true;
}

RECOIL_API bool StartRecoilCompensation()
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (!g_recoilEngine) {
        return false;
    }
    
    return g_recoilEngine->Start();
}

RECOIL_API bool StopRecoilCompensation()
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (!g_recoilEngine) {
        return false;
    }
    
    return g_recoilEngine->Stop();
}

RECOIL_API bool IsRecoilActive()
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (!g_recoilEngine) {
        return false;
    }
    
    return g_recoilEngine->IsActive();
}

RECOIL_API bool GetRecoilStats(RecoilStats* stats)
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (!g_recoilEngine || !stats) {
        return false;
    }
    
    *stats = g_recoilEngine->GetStats();
    return true;
}

RECOIL_API void ResetRecoilStats()
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (g_recoilEngine) {
        g_recoilEngine->ResetStats();
    }
}

RECOIL_API bool SetRecoilPattern(const double* horizontalPattern, const double* verticalPattern, int patternLength)
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (!g_recoilEngine || !horizontalPattern || !verticalPattern || patternLength <= 0) {
        return false;
    }
    
    std::vector<double> horizontal(horizontalPattern, horizontalPattern + patternLength);
    std::vector<double> vertical(verticalPattern, verticalPattern + patternLength);
    
    return g_recoilEngine->SetPattern(horizontal, vertical);
}

RECOIL_API bool EnableAdaptiveRecoil(bool enable)
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (!g_recoilEngine) {
        return false;
    }
    
    g_recoilEngine->EnableAdaptive(enable);
    return true;
}

RECOIL_API bool AddSupportedGame(const char* executableName)
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (!g_recoilEngine || !executableName) {
        return false;
    }
    
    return g_recoilEngine->AddSupportedGame(std::string(executableName));
}

RECOIL_API bool RemoveSupportedGame(const char* executableName)
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (!g_recoilEngine || !executableName) {
        return false;
    }
    
    return g_recoilEngine->RemoveSupportedGame(std::string(executableName));
}

RECOIL_API bool IsInSupportedGame()
{
    std::lock_guard<std::mutex> lock(g_engineMutex);
    
    if (!g_recoilEngine) {
        return false;
    }
    
    return g_recoilEngine->IsInSupportedGame();
}

} // extern "C"

// RecoilEngine Implementation
RecoilEngine::RecoilEngine()
    : m_running(false)
    , m_active(false)
    , m_patternIndex(0)
    , m_usePattern(false)
    , m_adaptiveEnabled(false)
    , m_currentStep(0)
    , m_currentX(0.0f)
    , m_currentY(0.0f)
    , m_recoilActive(false)
    , m_ignoreNextMouseMove(false)
    , m_targetWindow(nullptr)
{
    // Initialize default config
    m_config.horizontalStrength = 0.0;
    m_config.verticalStrength = 0.0;
    m_config.sensitivity = 1.0;
    m_config.delayMs = 0.0;
    m_config.activationKey = VK_LBUTTON;
    m_config.enabled = false;
    m_config.useRandomization = true;
    m_config.randomizationFactor = 0.2;
    m_config.smoothness = 0.5;
    
    // Initialize stats
    memset(&m_stats, 0, sizeof(m_stats));
    
    // Initialize supported games with defaults
    m_supportedGames = DEFAULT_SUPPORTED_GAMES;
    
    s_instance = this;
}

RecoilEngine::~RecoilEngine()
{
    Shutdown();
    s_instance = nullptr;
}

bool RecoilEngine::Initialize()
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    
    if (m_running) {
        return true; // Already initialized
    }
    
    ResetRecoilState();
    return true;
}

void RecoilEngine::Shutdown()
{
    Stop();
}

bool RecoilEngine::SetConfig(const RecoilConfig& config)
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_config = config;
    return true;
}

RecoilConfig RecoilEngine::GetConfig() const
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_config;
}

bool RecoilEngine::Start()
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    
    if (m_running) {
        return true;
    }
    
    m_running = true;
    m_recoilThread = std::make_unique<std::thread>(&RecoilEngine::RecoilThread, this);
    
    return true;
}

bool RecoilEngine::Stop()
{
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        m_running = false;
        m_active = false;
    }
    
    if (m_recoilThread && m_recoilThread->joinable()) {
        m_recoilThread->join();
        m_recoilThread.reset();
    }
    
    return true;
}

bool RecoilEngine::IsActive() const
{
    return m_active;
}

RecoilStats RecoilEngine::GetStats() const
{
    std::lock_guard<std::mutex> lock(m_statsMutex);
    RecoilStats stats = m_stats;
    stats.inGameWindow = const_cast<RecoilEngine*>(this)->IsInSupportedGame();
    strcpy_s(stats.currentGame, sizeof(stats.currentGame), m_currentGame.c_str());
    return stats;
}

void RecoilEngine::ResetStats()
{
    std::lock_guard<std::mutex> lock(m_statsMutex);
    memset(&m_stats, 0, sizeof(m_stats));
}

bool RecoilEngine::SetPattern(const std::vector<double>& horizontal, const std::vector<double>& vertical)
{
    std::lock_guard<std::mutex> lock(m_configMutex);

    if (horizontal.size() != vertical.size() || horizontal.empty()) {
        return false;
    }

    m_horizontalPattern = horizontal;
    m_verticalPattern = vertical;
    m_usePattern = true;
    m_patternIndex = 0;

    return true;
}

void RecoilEngine::EnableAdaptive(bool enable)
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_adaptiveEnabled = enable;
}

bool RecoilEngine::AddSupportedGame(const std::string& executableName)
{
    std::lock_guard<std::mutex> lock(m_gamesMutex);

    std::string lowerName = executableName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);

    m_supportedGames.insert(lowerName);
    return true;
}

bool RecoilEngine::RemoveSupportedGame(const std::string& executableName)
{
    std::lock_guard<std::mutex> lock(m_gamesMutex);

    std::string lowerName = executableName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);

    m_supportedGames.erase(lowerName);
    return true;
}

bool RecoilEngine::IsInSupportedGame()
{
    return CheckGameWindow();
}

void RecoilEngine::RecoilThread()
{
    while (m_running) {
        try {
            UpdateRecoil();
            std::this_thread::sleep_for(std::chrono::milliseconds(1)); // 1ms precision
        }
        catch (...) {
            // Continue running even if there's an error
        }
    }
}

void RecoilEngine::UpdateRecoil()
{
    RecoilConfig config;
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        config = m_config;
    }

    if (!config.enabled) {
        m_active = false;
        return;
    }

    // Check if we're in a supported game
    if (!CheckGameWindow()) {
        m_active = false;
        return;
    }

    bool isActivationKeyPressed = IsActivationKeyPressed();

    if (isActivationKeyPressed && !m_active) {
        // Start recoil compensation
        m_active = true;
        m_startTime = GetHighResTime();
        ResetRecoilState();

        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_stats.totalCompensations++;
    }
    else if (!isActivationKeyPressed && m_active) {
        // Stop recoil compensation
        m_active = false;
        ResetRecoilState();
    }

    if (m_active) {
        double elapsedMs = GetElapsedMs(m_startTime);

        if (elapsedMs < config.delayMs) {
            return;
        }

        // For immediate response: Skip timing check on first recoil step (based on Hermes)
        auto currentTime = GetHighResTime();
        double timeSinceLastUpdate = GetElapsedMs(m_lastUpdateTime);
        if (m_currentStep > 0 && timeSinceLastUpdate < 3.0) { // 3ms minimum interval
            return;
        }

        // Calculate next recoil values
        float recoilX = 0.0f;
        float recoilY = 0.0f;
        CalculateNextRecoilValues(recoilX, recoilY);

        // Apply the recoil compensation
        ApplyRecoilCompensation(recoilX, recoilY);

        // Update state
        m_lastUpdateTime = currentTime;
        m_currentStep++;

        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_stats.totalMouseMovements++;
    }
}

bool RecoilEngine::CheckGameWindow()
{
    static auto lastCheckTime = GetHighResTime();
    static bool lastResult = false;

    // Only check every 100ms to avoid performance impact
    auto currentTime = GetHighResTime();
    if (GetElapsedMs(lastCheckTime) < 100.0) {
        return lastResult;
    }
    lastCheckTime = currentTime;

    HWND foregroundWindow = GetForegroundWindow();
    if (!foregroundWindow) {
        lastResult = false;
        return false;
    }

    DWORD processId = 0;
    GetWindowThreadProcessId(foregroundWindow, &processId);

    if (processId == 0) {
        lastResult = false;
        return false;
    }

    HANDLE hProcess = OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, FALSE, processId);
    if (!hProcess) {
        lastResult = false;
        return false;
    }

    char processName[MAX_PATH];
    DWORD size = sizeof(processName);
    bool result = false;

    if (QueryFullProcessImageNameA(hProcess, 0, processName, &size)) {
        // Extract just the executable name
        const char* filename = strrchr(processName, '\\');
        if (filename) {
            filename++; // Skip the backslash
            std::string exeName = filename;
            std::transform(exeName.begin(), exeName.end(), exeName.begin(), ::tolower);

            std::lock_guard<std::mutex> lock(m_gamesMutex);
            result = (m_supportedGames.find(exeName) != m_supportedGames.end());

            if (result) {
                m_targetWindow = foregroundWindow;
                m_currentGame = exeName;
            }
        }
    }

    CloseHandle(hProcess);
    lastResult = result;
    return result;
}

// Calculate the next recoil compensation values (based on Hermes implementation)
void RecoilEngine::CalculateNextRecoilValues(float& x, float& y)
{
    RecoilConfig config;
    {
        std::lock_guard<std::mutex> lock(m_configMutex);
        config = m_config;
    }

    // Use pattern if available
    if (m_usePattern && !m_horizontalPattern.empty()) {
        size_t index = m_patternIndex % m_horizontalPattern.size();
        x = static_cast<float>(m_horizontalPattern[index]);
        y = static_cast<float>(m_verticalPattern[index]);
        m_patternIndex++;
        return;
    }

    // Base recoil values, scaled to reasonable pixel ranges (from Hermes)
    float baseVertical = static_cast<float>(config.verticalStrength * 0.08);
    float baseHorizontal = static_cast<float>(config.horizontalStrength * 0.04);

    // Pattern progression - can be customized for different recoil patterns
    float progressionFactor = std::min(1.0f, m_currentStep / 30.0f);
    baseVertical *= (1.0f + progressionFactor * 0.3f);
    baseHorizontal *= (1.0f + progressionFactor * 0.2f);

    // Add randomization
    if (config.useRandomization) {
        static std::mt19937 rng(static_cast<unsigned int>(std::chrono::system_clock::now().time_since_epoch().count()));
        std::uniform_real_distribution<float> dist(-1.0f, 1.0f);

        float randomX = dist(rng) * static_cast<float>(config.randomizationFactor) * std::abs(baseHorizontal) * 0.3f;
        float randomY = dist(rng) * static_cast<float>(config.randomizationFactor) * std::abs(baseVertical) * 0.2f;

        baseHorizontal += randomX;
        baseVertical += randomY;
    }

    // Calculate final recoil with smoothing
    float smoothingFactor = 1.0f - static_cast<float>(config.smoothness) * 0.95f;
    x = smoothingFactor * baseHorizontal + (1.0f - smoothingFactor) * m_currentX;
    y = smoothingFactor * baseVertical + (1.0f - smoothingFactor) * m_currentY;

    // Ensure minimum movement if recoil is enabled
    if (config.verticalStrength > 0 && y < 0.2f) y = 0.2f;
    if (config.verticalStrength < 0 && y > -0.2f) y = -0.2f;
    if (config.horizontalStrength > 0 && x < 0.2f) x = 0.2f;
    if (config.horizontalStrength < 0 && x > -0.2f) x = -0.2f;

    // Save current values for next smoothing calculation
    m_currentX = x;
    m_currentY = y;
}

// Apply the calculated recoil compensation by moving the mouse (based on Hermes)
void RecoilEngine::ApplyRecoilCompensation(float x, float y)
{
    // Calculate new position
    int deltaX = static_cast<int>(std::round(x));
    int deltaY = static_cast<int>(std::round(y));

    // Ensure minimum movement if recoil is enabled
    RecoilConfig config = GetConfig();
    if (config.verticalStrength != 0 && std::abs(deltaY) < 1) {
        deltaY = (config.verticalStrength > 0) ? 1 : -1;
    }
    if (config.horizontalStrength != 0 && std::abs(deltaX) < 1) {
        deltaX = (config.horizontalStrength > 0) ? 1 : -1;
    }

    // Skip if no movement
    if (deltaX == 0 && deltaY == 0) {
        return;
    }

    // Set flag to ignore the next mouse move event
    m_ignoreNextMouseMove = true;

    // Use SendInput to simulate mouse movement (works better with games)
    INPUT input = {0};
    input.type = INPUT_MOUSE;
    input.mi.dwFlags = MOUSEEVENTF_MOVE;
    input.mi.dx = deltaX;
    input.mi.dy = deltaY;

    // Send the input
    UINT result = SendInput(1, &input, sizeof(INPUT));
    if (result != 1) {
        // Only log errors occasionally to reduce performance impact
        static auto lastErrorLogTime = GetHighResTime();
        auto currentTime = GetHighResTime();
        if (GetElapsedMs(lastErrorLogTime) > 5000.0) { // Only log once every 5 seconds
            DWORD error = GetLastError();
            OutputDebugStringA(("[RECOIL] Failed to send input. Error: " + std::to_string(error) + "\n").c_str());
            lastErrorLogTime = currentTime;
        }
        // Reset flag if sending failed
        m_ignoreNextMouseMove = false;
    }
}

void RecoilEngine::ResetRecoilState()
{
    m_currentStep = 0;
    m_lastUpdateTime = GetHighResTime();
    m_currentX = 0.0f;
    m_currentY = 0.0f;
    m_recoilActive = false;
    m_ignoreNextMouseMove = false;
    m_patternIndex = 0;
}

bool RecoilEngine::IsActivationKeyPressed() const
{
    RecoilConfig config = GetConfig();
    return (GetAsyncKeyState(config.activationKey) & 0x8000) != 0;
}

std::chrono::high_resolution_clock::time_point RecoilEngine::GetHighResTime() const
{
    return std::chrono::high_resolution_clock::now();
}

double RecoilEngine::GetElapsedMs(const std::chrono::high_resolution_clock::time_point& start) const
{
    auto now = GetHighResTime();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now - start);
    return duration.count() / 1000.0; // Convert to milliseconds
}
