{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Setup\\FennecClient.Setup.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Core\\FennecClient.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Core\\FennecClient.Core.csproj", "projectName": "FennecClient.Core", "projectPath": "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Core\\FennecClient.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.Net.Sdk.Compilers.Toolset", "version": "[9.0.300, 9.0.300]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.16, 8.0.16]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Setup\\FennecClient.Setup.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Setup\\FennecClient.Setup.csproj", "projectName": "FennecClient.Setup", "projectPath": "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Setup\\FennecClient.Setup.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Setup\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Core\\FennecClient.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Fennec\\FennecClient.Core\\FennecClient.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.Net.Sdk.Compilers.Toolset", "version": "[9.0.300, 9.0.300]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.16, 8.0.16]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.16, 8.0.16]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}